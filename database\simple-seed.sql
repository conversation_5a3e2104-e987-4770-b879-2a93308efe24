-- 简单测试数据插入脚本
USE school_enrollment_todo;

-- 插入管理员用户
INSERT INTO users (openid, nickname, role, real_name, email, language_preference) VALUES
('admin001', 'Admin', 'super_admin', 'Admin', '<EMAIL>', 'zh_cn'),
('teacher001', 'Teacher', 'admin', 'Teacher', '<EMAIL>', 'zh_cn');

-- 插入测试学生
INSERT INTO users (openid, nickname, role, student_id, real_name, phone, email, language_preference) VALUES
('student001', 'Student1', 'student', 'STU001', 'Student1', '13800138001', '<EMAIL>', 'zh_cn'),
('student002', 'Student2', 'student', 'STU002', 'Student2', '13800138002', '<EMAIL>', 'en_us');

-- 插入待办事项模板
INSERT INTO todo_templates (title, title_en, description, description_en, category, priority, required, sort_order, form_config) VALUES
('身份验证', 'Identity Verification', '请上传身份证件', 'Please upload ID', '报到登记', 3, TRUE, 1, 
'{"fields": [{"name": "id_photo", "type": "file", "label": "证件照片", "label_en": "ID Photo", "required": true}]}'),

('学费缴纳', 'Tuition Payment', '请上传缴费凭证', 'Please upload receipt', '财务缴费', 3, TRUE, 2,
'{"fields": [{"name": "receipt", "type": "file", "label": "缴费凭证", "label_en": "Receipt", "required": true}]}');

-- 为学生创建待办事项
INSERT INTO student_todos (student_id, template_id, status) VALUES
(3, 1, 'pending'),
(3, 2, 'pending'),
(4, 1, 'completed'),
(4, 2, 'pending');

-- 插入多语言配置
INSERT INTO language_configs (config_key, zh_cn, en_us, category) VALUES
('app.title', '入学待办事项', 'Enrollment To-Do', 'app'),
('status.pending', '待处理', 'Pending', 'status'),
('status.completed', '已完成', 'Completed', 'status'),
('common.submit', '提交', 'Submit', 'button');

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('app_name', '入学待办事项系统', '应用名称'),
('default_language', 'zh_cn', '默认语言');
