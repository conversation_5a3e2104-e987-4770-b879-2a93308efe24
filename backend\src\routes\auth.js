const express = require('express');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const { User } = require('../models');
const config = require('../config/app');
const { validateRequest } = require('../middleware/validation');
const { body } = require('express-validator');

const router = express.Router();

/**
 * 测试登录接口（开发环境使用）
 * POST /api/auth/test-login
 */
router.post('/test-login', async (req, res) => {
  try {
    // 创建测试用户数据
    const testUser = {
      id: 1,
      openid: 'test_openid_001',
      nickname: '测试用户',
      realName: '测试学生',
      role: 'student',
      languagePreference: 'zh_cn',
      avatarUrl: '',
      phone: '13800138000',
      email: '<EMAIL>'
    };

    // 生成测试token
    const token = jwt.sign(
      {
        userId: testUser.id,
        openid: testUser.openid,
        role: testUser.role
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    res.json({
      message: '测试登录成功',
      token,
      user: testUser
    });

  } catch (error) {
    console.error('测试登录错误:', error);
    res.status(500).json({
      error: '测试登录失败',
      code: 'TEST_LOGIN_ERROR'
    });
  }
});

// 微信登录验证规则
const wechatLoginValidation = [
  body('code').notEmpty().withMessage('微信授权码不能为空'),
  body('userInfo').optional().isObject().withMessage('用户信息格式错误')
];

/**
 * 微信小程序登录
 * POST /api/auth/wechat-login
 */
router.post('/wechat-login', wechatLoginValidation, validateRequest, async (req, res) => {
  try {
    const { code, userInfo } = req.body;
    
    // 调用微信API获取session_key和openid
    const wechatResponse = await axios.get(config.wechat.loginUrl, {
      params: {
        appid: config.wechat.appId,
        secret: config.wechat.appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      }
    });
    
    const { openid, unionid, session_key, errcode, errmsg } = wechatResponse.data;
    
    if (errcode) {
      return res.status(400).json({
        error: '微信登录失败',
        code: 'WECHAT_LOGIN_ERROR',
        details: errmsg
      });
    }
    
    // 查找或创建用户
    let user = await User.findByOpenid(openid);
    
    if (!user) {
      // 创建新用户
      user = await User.create({
        openid,
        unionid,
        nickname: userInfo?.nickName || '微信用户',
        avatarUrl: userInfo?.avatarUrl || '',
        languagePreference: 'zh_cn' // 默认中文
      });
    } else {
      // 更新用户信息
      if (userInfo) {
        await user.update({
          nickname: userInfo.nickName || user.nickname,
          avatarUrl: userInfo.avatarUrl || user.avatarUrl,
          unionid: unionid || user.unionid
        });
      }
    }
    
    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        openid: user.openid,
        role: user.role
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    res.json({
      message: '登录成功',
      token,
      user: user.toJSON(),
      sessionKey: session_key // 用于解密用户敏感数据
    });
    
  } catch (error) {
    console.error('微信登录错误:', error);
    res.status(500).json({
      error: '登录失败',
      code: 'LOGIN_ERROR'
    });
  }
});

/**
 * 获取当前用户信息
 * GET /api/auth/me
 */
router.get('/me', require('../middleware/auth').authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.userId, {
      attributes: { exclude: ['openid'] }
    });
    
    if (!user) {
      return res.status(404).json({
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }
    
    res.json({
      user: user.toJSON()
    });
    
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      error: '获取用户信息失败',
      code: 'GET_USER_ERROR'
    });
  }
});

/**
 * 更新用户信息
 * PUT /api/auth/profile
 */
router.put('/profile',
  require('../middleware/auth').authenticateToken,
  [
    body('realName').optional().isLength({ min: 1, max: 50 }).withMessage('真实姓名长度应在1-50字符之间'),
    body('phone').optional().isMobilePhone('zh-CN').withMessage('手机号格式错误'),
    body('email').optional().isEmail().withMessage('邮箱格式错误'),
    body('languagePreference').optional().isIn(['zh_cn', 'en_us']).withMessage('语言偏好值无效')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { realName, phone, email, languagePreference } = req.body;
      
      const user = await User.findByPk(req.user.userId);
      if (!user) {
        return res.status(404).json({
          error: '用户不存在',
          code: 'USER_NOT_FOUND'
        });
      }
      
      // 更新用户信息
      await user.update({
        realName: realName || user.realName,
        phone: phone || user.phone,
        email: email || user.email,
        languagePreference: languagePreference || user.languagePreference
      });
      
      res.json({
        message: '用户信息更新成功',
        user: user.toJSON()
      });
      
    } catch (error) {
      console.error('更新用户信息错误:', error);
      res.status(500).json({
        error: '更新用户信息失败',
        code: 'UPDATE_USER_ERROR'
      });
    }
  }
);

/**
 * 刷新token
 * POST /api/auth/refresh
 */
router.post('/refresh', require('../middleware/auth').authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.userId);
    if (!user) {
      return res.status(404).json({
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }
    
    // 生成新的token
    const token = jwt.sign(
      {
        userId: user.id,
        openid: user.openid,
        role: user.role
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    res.json({
      message: 'Token刷新成功',
      token
    });
    
  } catch (error) {
    console.error('刷新token错误:', error);
    res.status(500).json({
      error: '刷新token失败',
      code: 'REFRESH_TOKEN_ERROR'
    });
  }
});

module.exports = router;
