const express = require('express');
const router = express.Router();

// 测试待办事项数据
const mockTodos = [
  {
    id: 1,
    title: '身份验证',
    titleEn: 'Identity Verification',
    status: 'completed',
    priority: 3,
    category: '报到登记',
    updatedAt: '2024-12-01 10:30:00'
  },
  {
    id: 2,
    title: '学费缴纳',
    titleEn: 'Tuition Payment',
    status: 'pending',
    priority: 3,
    category: '财务缴费',
    updatedAt: '2024-12-01 09:15:00'
  },
  {
    id: 3,
    title: '体检报告提交',
    titleEn: 'Medical Report Submission',
    status: 'processing',
    priority: 2,
    category: '健康管理',
    updatedAt: '2024-11-30 16:45:00'
  },
  {
    id: 4,
    title: '宿舍分配确认',
    titleEn: 'Dormitory Assignment Confirmation',
    status: 'pending',
    priority: 2,
    category: '住宿安排',
    updatedAt: '2024-11-30 14:20:00'
  },
  {
    id: 5,
    title: '课程选择',
    titleEn: 'Course Selection',
    status: 'pending',
    priority: 2,
    category: '学务办理',
    updatedAt: '2024-11-29 11:10:00'
  },
  {
    id: 6,
    title: '签证信息确认',
    titleEn: 'Visa Information Confirmation',
    status: 'processing',
    priority: 3,
    category: '国际学生专项',
    updatedAt: '2024-11-28 09:30:00'
  }
];

/**
 * 获取学生待办列表
 * GET /api/todos/student/list
 */
router.get('/student/list', (req, res) => {
  try {
    res.json({
      success: true,
      data: mockTodos,
      total: mockTodos.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '获取待办列表失败'
    });
  }
});

/**
 * 获取学生进度统计
 * GET /api/todos/student/progress
 */
router.get('/student/progress', (req, res) => {
  try {
    const total = mockTodos.length;
    const completed = mockTodos.filter(todo => todo.status === 'completed').length;
    const processing = mockTodos.filter(todo => todo.status === 'processing').length;
    const pending = mockTodos.filter(todo => todo.status === 'pending').length;
    const percentage = Math.round((completed / total) * 100);

    const recentTodos = mockTodos
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
      .slice(0, 3);

    res.json({
      success: true,
      progress: {
        total,
        completed,
        processing,
        pending,
        percentage
      },
      recentTodos
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '获取进度统计失败'
    });
  }
});

/**
 * 获取待办事项详情
 * GET /api/todos/:id
 */
router.get('/:id', (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const todo = mockTodos.find(t => t.id === id);

    if (!todo) {
      return res.status(404).json({
        success: false,
        error: '待办事项不存在'
      });
    }

    res.json({
      success: true,
      data: todo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '获取待办事项详情失败'
    });
  }
});

router.get('/', (req, res) => {
  res.json({ message: 'Todos API - 请使用具体的端点' });
});

module.exports = router;
