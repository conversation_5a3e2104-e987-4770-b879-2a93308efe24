<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>入学待办事项</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/mobile.css">
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="app-container" style="display: none;">
        <!-- 头部导航 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">入学待办事项</h1>
                <div class="header-actions">
                    <button id="languageBtn" class="btn-icon" title="切换语言">
                        <span class="icon">🌐</span>
                    </button>
                    <button id="userBtn" class="btn-icon" title="用户菜单">
                        <span class="icon">👤</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="app-main">
            <!-- 登录页面 -->
            <div id="loginPage" class="page active">
                <div class="login-container">
                    <div class="login-header">
                        <h2>欢迎登录</h2>
                        <p>请输入您的登录信息</p>
                    </div>
                    
                    <form id="loginForm" class="login-form">
                        <div class="form-group">
                            <label for="username">用户名/手机号</label>
                            <input type="text" id="username" name="username" required 
                                   placeholder="请输入用户名或手机号">
                        </div>
                        
                        <div class="form-group">
                            <label for="password">密码</label>
                            <input type="password" id="password" name="password" required 
                                   placeholder="请输入密码">
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block" id="loginBtn">
                            <span class="btn-text">登录</span>
                            <span class="btn-loading" style="display: none;">登录中...</span>
                        </button>
                        
                        <button type="button" class="btn btn-secondary btn-block" id="testLoginBtn">
                            测试登录（开发环境）
                        </button>
                    </form>
                    
                    <div class="login-footer">
                        <p>首次使用？请联系管理员获取账号</p>
                    </div>
                </div>
            </div>

            <!-- 首页 -->
            <div id="homePage" class="page">
                <div class="page-content">
                    <!-- 欢迎卡片 -->
                    <div class="welcome-card card">
                        <div class="welcome-content">
                            <div class="welcome-text">
                                <h3>欢迎回来</h3>
                                <p id="userName">测试用户</p>
                            </div>
                            <div class="welcome-avatar">
                                <span class="avatar-icon">👤</span>
                            </div>
                        </div>
                    </div>

                    <!-- 进度卡片 -->
                    <div class="progress-card card">
                        <div class="card-header">
                            <h3>整体进度</h3>
                        </div>
                        <div class="progress-content">
                            <div class="progress-circle">
                                <svg class="progress-svg" viewBox="0 0 100 100">
                                    <circle cx="50" cy="50" r="45" class="progress-bg"></circle>
                                    <circle cx="50" cy="50" r="45" class="progress-bar" id="progressCircle"></circle>
                                </svg>
                                <div class="progress-text">
                                    <span id="progressPercent">0%</span>
                                </div>
                            </div>
                            <div class="progress-stats">
                                <div class="stat-item">
                                    <span class="stat-number" id="totalCount">0</span>
                                    <span class="stat-label">总计</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number text-success" id="completedCount">0</span>
                                    <span class="stat-label">已完成</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number text-warning" id="pendingCount">0</span>
                                    <span class="stat-label">待处理</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近待办 -->
                    <div class="recent-todos card">
                        <div class="card-header">
                            <h3>最近待办</h3>
                            <button class="btn-link" onclick="showPage('todosPage')">查看全部</button>
                        </div>
                        <div id="recentTodosList" class="todos-list">
                            <!-- 动态加载 -->
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="quick-actions">
                        <button class="action-btn" onclick="showPage('todosPage')">
                            <span class="action-icon">📋</span>
                            <span class="action-text">待办事项</span>
                        </button>
                        <button class="action-btn" onclick="showPage('profilePage')">
                            <span class="action-icon">👤</span>
                            <span class="action-text">个人中心</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 待办事项页面 -->
            <div id="todosPage" class="page">
                <div class="page-header">
                    <button class="btn-back" onclick="showPage('homePage')">
                        <span class="icon">←</span>
                    </button>
                    <h2>待办事项</h2>
                    <div class="header-actions">
                        <button class="btn-icon" id="filterBtn">
                            <span class="icon">🔍</span>
                        </button>
                    </div>
                </div>
                <div class="page-content">
                    <div id="todosList" class="todos-list">
                        <!-- 动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 个人中心页面 -->
            <div id="profilePage" class="page">
                <div class="page-header">
                    <button class="btn-back" onclick="showPage('homePage')">
                        <span class="icon">←</span>
                    </button>
                    <h2>个人中心</h2>
                </div>
                <div class="page-content">
                    <div class="profile-info card">
                        <div class="profile-avatar">
                            <span class="avatar-icon">👤</span>
                        </div>
                        <div class="profile-details">
                            <h3 id="profileName">测试用户</h3>
                            <p id="profileRole">学生</p>
                        </div>
                    </div>

                    <div class="profile-menu">
                        <button class="menu-item">
                            <span class="menu-icon">🌐</span>
                            <span class="menu-text">语言设置</span>
                            <span class="menu-arrow">→</span>
                        </button>
                        <button class="menu-item">
                            <span class="menu-icon">📱</span>
                            <span class="menu-text">联系方式</span>
                            <span class="menu-arrow">→</span>
                        </button>
                        <button class="menu-item">
                            <span class="menu-icon">❓</span>
                            <span class="menu-text">帮助中心</span>
                            <span class="menu-arrow">→</span>
                        </button>
                        <button class="menu-item" id="logoutBtn">
                            <span class="menu-icon">🚪</span>
                            <span class="menu-text">退出登录</span>
                            <span class="menu-arrow">→</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部导航 -->
        <nav class="app-nav">
            <button class="nav-item active" onclick="showPage('homePage')" data-page="homePage">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">首页</span>
            </button>
            <button class="nav-item" onclick="showPage('todosPage')" data-page="todosPage">
                <span class="nav-icon">📋</span>
                <span class="nav-text">待办</span>
            </button>
            <button class="nav-item" onclick="showPage('profilePage')" data-page="profilePage">
                <span class="nav-icon">👤</span>
                <span class="nav-text">我的</span>
            </button>
        </nav>
    </div>

    <!-- 提示框 -->
    <div id="toast" class="toast"></div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button class="modal-close" onclick="hideModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                内容
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="hideModal()">取消</button>
                <button class="btn btn-primary" id="modalConfirm">确定</button>
            </div>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
