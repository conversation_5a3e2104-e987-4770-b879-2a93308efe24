/* app.wxss - 全局样式 */

/* 全局变量 */
page {
  --primary-color: #1976d2;
  --primary-light: #42a5f5;
  --primary-dark: #1565c0;
  --secondary-color: #ff9800;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --text-primary: #212121;
  --text-secondary: #757575;
  --divider-color: #e0e0e0;
  --background-color: #f5f5f5;
  --surface-color: #ffffff;
}

/* 基础样式重置 */
view, text, button, input, textarea {
  box-sizing: border-box;
}

page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-color);
}

/* 容器样式 */
.container {
  padding: 32rpx;
}

.page-container {
  min-height: 100vh;
  background-color: var(--background-color);
}

/* 卡片样式 */
.card {
  background-color: var(--surface-color);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
  overflow: hidden;
}

.card-header {
  padding: 32rpx;
  border-bottom: 1rpx solid var(--divider-color);
}

.card-body {
  padding: 32rpx;
}

.card-footer {
  padding: 24rpx 32rpx;
  background-color: #fafafa;
  border-top: 1rpx solid var(--divider-color);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
  min-height: 88rpx;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-error {
  background-color: var(--error-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-text {
  background-color: transparent;
  color: var(--primary-color);
  padding: 16rpx 24rpx;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  min-height: 64rpx;
}

.btn-large {
  padding: 28rpx 48rpx;
  font-size: 32rpx;
  min-height: 96rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

.btn:disabled {
  opacity: 0.6;
}

/* 表单样式 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid var(--divider-color);
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: var(--surface-color);
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-textarea {
  min-height: 120rpx;
}

.form-select {
  padding-right: 64rpx;
}

/* 状态样式 */
.status-badge {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
}

.status-pending {
  background-color: #fff3e0;
  color: #e65100;
}

.status-processing {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.status-completed {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-need-supplement {
  background-color: #fff8e1;
  color: #f57c00;
}

.status-exception {
  background-color: #ffebee;
  color: #c62828;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 16rpx; }
.mb-2 { margin-bottom: 32rpx; }
.mb-3 { margin-bottom: 48rpx; }
.mb-4 { margin-bottom: 64rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 16rpx; }
.mt-2 { margin-top: 32rpx; }
.mt-3 { margin-top: 48rpx; }
.mt-4 { margin-top: 64rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 16rpx; }
.p-2 { padding: 32rpx; }
.p-3 { padding: 48rpx; }
.p-4 { padding: 64rpx; }

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 4rpx solid var(--divider-color);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
