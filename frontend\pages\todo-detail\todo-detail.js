// pages/todo-detail/todo-detail.js
const app = getApp();
const { t } = require('../../utils/i18n');

Page({
  data: {
    todoId: null,
    todo: null,
    loading: true,
    language: 'zh_cn'
  },

  onLoad(options) {
    console.log('待办详情页面加载');
    const { id } = options;
    
    this.setData({
      todoId: id,
      language: app.globalData.language
    });
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: t('todos.detail')
    });
    
    // 检查登录状态
    if (!app.globalData.token) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    
    this.loadTodoDetail();
  },

  // 语言变更回调
  onLanguageChange(language) {
    this.setData({ language });
    wx.setNavigationBarTitle({
      title: t('todos.detail')
    });
  },

  // 加载待办详情
  async loadTodoDetail() {
    try {
      this.setData({ loading: true });
      
      // 这里应该调用实际的API
      // const response = await app.request({
      //   url: `/todos/${this.data.todoId}`
      // });
      
      // 模拟数据
      const mockTodo = {
        id: this.data.todoId,
        title: '身份验证',
        titleEn: 'Identity Verification',
        description: '请上传身份证或护照照片进行身份验证',
        descriptionEn: 'Please upload ID card or passport photo for identity verification',
        category: '报到登记',
        categoryEn: 'Registration',
        status: 'pending',
        priority: 3,
        required: true,
        updatedAt: '2024-12-01 10:30:00'
      };
      
      this.setData({
        todo: mockTodo,
        loading: false
      });
      
    } catch (error) {
      console.error('加载待办详情失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: t('common.error'),
        icon: 'error'
      });
    }
  }
});
