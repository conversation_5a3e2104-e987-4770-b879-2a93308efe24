// 国际化工具
const app = getApp();

// 语言包
const languages = {
  zh_cn: {
    // 通用
    'app.title': '入学待办事项',
    'app.welcome': '欢迎使用入学待办事项系统',
    'common.confirm': '确认',
    'common.cancel': '取消',
    'common.submit': '提交',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.save': '保存',
    'common.back': '返回',
    'common.loading': '加载中...',
    'common.success': '操作成功',
    'common.error': '操作失败',
    'common.retry': '重试',
    'common.refresh': '刷新',
    'common.minutes_ago': '分钟前',
    'common.hours_ago': '小时前',
    
    // 状态
    'status.pending': '待处理',
    'status.processing': '进行中',
    'status.completed': '已完成',
    'status.need_supplement': '需补充',
    'status.exception': '异常',
    
    // 登录页面
    'login.title': '微信登录',
    'login.welcome': '欢迎使用入学待办事项系统',
    'login.wechat_login': '微信一键登录',
    'login.login_success': '登录成功',
    'login.login_failed': '登录失败',
    
    // 首页
    'home.title': '首页',
    'home.welcome': '欢迎，{name}',
    'home.progress': '整体进度',
    'home.total_todos': '总待办事项',
    'home.completed_todos': '已完成',
    'home.pending_todos': '待处理',
    'home.recent_todos': '最近待办',
    'home.view_all': '查看全部',
    
    // 待办事项
    'todos.title': '待办事项',
    'todos.all': '全部',
    'todos.pending': '待处理',
    'todos.completed': '已完成',
    'todos.filter': '筛选',
    'todos.search': '搜索待办事项',
    'todos.no_data': '暂无待办事项',
    'todos.detail': '详情',
    'todos.submit': '提交',
    'todos.upload_file': '上传文件',
    'todos.select_option': '请选择',
    'todos.required_field': '必填项',
    'todos.submit_success': '提交成功',
    'todos.submit_failed': '提交失败',
    
    // 个人中心
    'profile.title': '个人中心',
    'profile.info': '个人信息',
    'profile.real_name': '真实姓名',
    'profile.student_id': '学号',
    'profile.phone': '手机号',
    'profile.email': '邮箱',
    'profile.language': '语言设置',
    'profile.logout': '退出登录',
    'profile.edit_info': '编辑信息',
    'profile.save_success': '保存成功',
    'profile.save_failed': '保存失败',
    
    // 管理员
    'admin.title': '管理后台',
    'admin.students': '学生管理',
    'admin.templates': '模板管理',
    'admin.statistics': '数据统计',
    'admin.student_list': '学生列表',
    'admin.student_progress': '学生进度',
    'admin.batch_operation': '批量操作',
    'admin.mark_completed': '标记完成',
    'admin.mark_pending': '标记待处理',
    'admin.add_remark': '添加备注',
    
    // 表单
    'form.text': '文本',
    'form.select': '选择',
    'form.file': '文件',
    'form.date': '日期',
    'form.number': '数字',
    'form.textarea': '长文本',
    'form.required': '必填',
    'form.optional': '选填',
    'form.placeholder': '请输入',
    'form.select_placeholder': '请选择',
    'form.file_placeholder': '请选择文件',
    'form.date_placeholder': '请选择日期',
    
    // 错误信息
    'error.network': '网络连接失败',
    'error.server': '服务器错误',
    'error.unauthorized': '未授权访问',
    'error.forbidden': '权限不足',
    'error.not_found': '资源不存在',
    'error.validation': '数据验证失败',
    'error.file_too_large': '文件过大',
    'error.invalid_file_type': '文件类型不支持'
  },
  
  en_us: {
    // Common
    'app.title': 'Enrollment To-Do',
    'app.welcome': 'Welcome to Enrollment To-Do System',
    'common.confirm': 'Confirm',
    'common.cancel': 'Cancel',
    'common.submit': 'Submit',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.save': 'Save',
    'common.back': 'Back',
    'common.loading': 'Loading...',
    'common.success': 'Success',
    'common.error': 'Error',
    'common.retry': 'Retry',
    'common.refresh': 'Refresh',
    'common.minutes_ago': ' minutes ago',
    'common.hours_ago': ' hours ago',
    
    // Status
    'status.pending': 'Pending',
    'status.processing': 'Processing',
    'status.completed': 'Completed',
    'status.need_supplement': 'Need Supplement',
    'status.exception': 'Exception',
    
    // Login
    'login.title': 'WeChat Login',
    'login.welcome': 'Welcome to Enrollment To-Do System',
    'login.wechat_login': 'Login with WeChat',
    'login.login_success': 'Login successful',
    'login.login_failed': 'Login failed',
    
    // Home
    'home.title': 'Home',
    'home.welcome': 'Welcome, {name}',
    'home.progress': 'Overall Progress',
    'home.total_todos': 'Total To-Dos',
    'home.completed_todos': 'Completed',
    'home.pending_todos': 'Pending',
    'home.recent_todos': 'Recent To-Dos',
    'home.view_all': 'View All',
    
    // To-Dos
    'todos.title': 'To-Do Items',
    'todos.all': 'All',
    'todos.pending': 'Pending',
    'todos.completed': 'Completed',
    'todos.filter': 'Filter',
    'todos.search': 'Search to-dos',
    'todos.no_data': 'No to-do items',
    'todos.detail': 'Detail',
    'todos.submit': 'Submit',
    'todos.upload_file': 'Upload File',
    'todos.select_option': 'Please select',
    'todos.required_field': 'Required',
    'todos.submit_success': 'Submitted successfully',
    'todos.submit_failed': 'Submit failed',
    
    // Profile
    'profile.title': 'Profile',
    'profile.info': 'Personal Information',
    'profile.real_name': 'Real Name',
    'profile.student_id': 'Student ID',
    'profile.phone': 'Phone',
    'profile.email': 'Email',
    'profile.language': 'Language',
    'profile.logout': 'Logout',
    'profile.edit_info': 'Edit Info',
    'profile.save_success': 'Saved successfully',
    'profile.save_failed': 'Save failed',
    
    // Admin
    'admin.title': 'Admin Panel',
    'admin.students': 'Student Management',
    'admin.templates': 'Template Management',
    'admin.statistics': 'Statistics',
    'admin.student_list': 'Student List',
    'admin.student_progress': 'Student Progress',
    'admin.batch_operation': 'Batch Operation',
    'admin.mark_completed': 'Mark Completed',
    'admin.mark_pending': 'Mark Pending',
    'admin.add_remark': 'Add Remark',
    
    // Form
    'form.text': 'Text',
    'form.select': 'Select',
    'form.file': 'File',
    'form.date': 'Date',
    'form.number': 'Number',
    'form.textarea': 'Textarea',
    'form.required': 'Required',
    'form.optional': 'Optional',
    'form.placeholder': 'Please enter',
    'form.select_placeholder': 'Please select',
    'form.file_placeholder': 'Please select file',
    'form.date_placeholder': 'Please select date',
    
    // Errors
    'error.network': 'Network connection failed',
    'error.server': 'Server error',
    'error.unauthorized': 'Unauthorized access',
    'error.forbidden': 'Insufficient permissions',
    'error.not_found': 'Resource not found',
    'error.validation': 'Data validation failed',
    'error.file_too_large': 'File too large',
    'error.invalid_file_type': 'Invalid file type'
  }
};

// 获取翻译文本
function t(key, params = {}) {
  const currentLanguage = app.globalData.language || 'zh_cn';
  const languageData = languages[currentLanguage] || languages.zh_cn;
  
  let text = languageData[key] || key;
  
  // 替换参数
  Object.keys(params).forEach(param => {
    text = text.replace(`{${param}}`, params[param]);
  });
  
  return text;
}

// 获取当前语言
function getCurrentLanguage() {
  return app.globalData.language || 'zh_cn';
}

// 设置语言
function setLanguage(language) {
  app.setLanguage(language);
}

// 获取支持的语言列表
function getSupportedLanguages() {
  return [
    { value: 'zh_cn', label: '中文' },
    { value: 'en_us', label: 'English' }
  ];
}

module.exports = {
  t,
  getCurrentLanguage,
  setLanguage,
  getSupportedLanguages
};
