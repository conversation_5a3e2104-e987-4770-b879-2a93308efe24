// API 模块
class API {
    constructor() {
        this.baseURL = 'http://localhost:3000/api';
        this.token = localStorage.getItem('token');
    }

    // 设置token
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('token', token);
        } else {
            localStorage.removeItem('token');
        }
    }

    // 获取请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        return headers;
    }

    // 通用请求方法
    async request(url, options = {}) {
        const config = {
            headers: this.getHeaders(),
            ...options
        };

        try {
            const response = await fetch(`${this.baseURL}${url}`, config);
            
            // 检查响应状态
            if (!response.ok) {
                if (response.status === 401) {
                    // 未授权，清除token并跳转到登录页
                    this.setToken(null);
                    showPage('loginPage');
                    throw new Error('登录已过期，请重新登录');
                }
                
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // GET 请求
    async get(url) {
        return this.request(url, { method: 'GET' });
    }

    // POST 请求
    async post(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT 请求
    async put(url, data) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE 请求
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }

    // 认证相关API
    async login(credentials) {
        const response = await this.post('/auth/login', credentials);
        if (response.token) {
            this.setToken(response.token);
        }
        return response;
    }

    async testLogin() {
        const response = await this.post('/auth/test-login', {});
        if (response.token) {
            this.setToken(response.token);
        }
        return response;
    }

    async logout() {
        try {
            await this.post('/auth/logout', {});
        } catch (error) {
            console.warn('登出请求失败:', error);
        } finally {
            this.setToken(null);
        }
    }

    async getCurrentUser() {
        return this.get('/auth/me');
    }

    // 待办事项相关API
    async getTodos() {
        return this.get('/todos/student/list');
    }

    async getTodoProgress() {
        return this.get('/todos/student/progress');
    }

    async getTodoDetail(id) {
        return this.get(`/todos/${id}`);
    }

    async updateTodoStatus(id, status) {
        return this.put(`/todos/${id}/status`, { status });
    }

    // 用户相关API
    async updateProfile(data) {
        return this.put('/users/profile', data);
    }

    async uploadFile(file, type = 'document') {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);

        const response = await fetch(`${this.baseURL}/files/upload`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error('文件上传失败');
        }

        return response.json();
    }

    // 健康检查
    async healthCheck() {
        try {
            const response = await fetch('http://localhost:3000/health');
            return response.json();
        } catch (error) {
            throw new Error('无法连接到服务器');
        }
    }
}

// 创建全局API实例
window.api = new API();
