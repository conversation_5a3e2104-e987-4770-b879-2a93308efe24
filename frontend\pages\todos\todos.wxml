<!--pages/todos/todos.wxml-->
<view class="page-container">
  <!-- 搜索和筛选区域 -->
  <view class="search-filter-section">
    <!-- 搜索框 -->
    <view class="search-container">
      <input
        class="search-input"
        placeholder="{{language === 'en_us' ? 'Search to-dos...' : '搜索待办事项...'}}"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
      />
      <text class="search-emoji">🔍</text>
    </view>
    
    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}"
        data-filter="all"
        bindtap="onFilterChange"
      >
        {{language === 'en_us' ? 'All' : '全部'}}
      </view>
      <view 
        class="filter-tab {{currentFilter === 'pending' ? 'active' : ''}}"
        data-filter="pending"
        bindtap="onFilterChange"
      >
        {{language === 'en_us' ? 'Pending' : '待处理'}}
      </view>
      <view 
        class="filter-tab {{currentFilter === 'completed' ? 'active' : ''}}"
        data-filter="completed"
        bindtap="onFilterChange"
      >
        {{language === 'en_us' ? 'Completed' : '已完成'}}
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{language === 'en_us' ? 'Loading...' : '加载中...'}}</text>
  </view>

  <!-- 待办事项列表 -->
  <view wx:else class="todos-container">
    <!-- 空状态 -->
    <view wx:if="{{filteredTodos.length === 0}}" class="empty-state">
      <view class="empty-emoji">📝</view>
      <text class="empty-text">
        {{language === 'en_us' ? 'No to-do items found' : '暂无待办事项'}}
      </text>
      <text class="empty-desc">
        {{language === 'en_us' ? 'Try adjusting your search or filter' : '尝试调整搜索条件或筛选器'}}
      </text>
    </view>

    <!-- 待办事项列表 -->
    <view wx:else class="todos-list">
      <view 
        wx:for="{{filteredTodos}}" 
        wx:key="id" 
        class="todo-item card"
        bindtap="goToDetail"
        data-id="{{item.id}}"
      >
        <!-- 待办事项头部 -->
        <view class="todo-header">
          <view class="todo-title-section">
            <text class="todo-title">
              {{language === 'en_us' && item.titleEn ? item.titleEn : item.title}}
            </text>
            <view class="todo-badges">
              <!-- 必需标识 -->
              <view wx:if="{{item.required}}" class="badge required-badge">
                {{language === 'en_us' ? 'Required' : '必需'}}
              </view>
              <!-- 优先级标识 -->
              <view class="badge priority-badge priority-{{item.priority}}">
                {{language === 'en_us' ? 
                  (item.priority === 3 ? 'High' : item.priority === 2 ? 'Medium' : 'Low') :
                  (item.priority === 3 ? '高' : item.priority === 2 ? '中' : '低')
                }}
              </view>
            </view>
          </view>
          
          <!-- 状态标识 -->
          <view class="status-badge status-{{item.status}}">
            {{language === 'en_us' ? 
              (item.status === 'pending' ? 'Pending' : 
               item.status === 'processing' ? 'Processing' : 
               item.status === 'completed' ? 'Completed' : 
               item.status === 'need_supplement' ? 'Need Supplement' : 'Exception') :
              (item.status === 'pending' ? '待处理' : 
               item.status === 'processing' ? '进行中' : 
               item.status === 'completed' ? '已完成' : 
               item.status === 'need_supplement' ? '需补充' : '异常')
            }}
          </view>
        </view>

        <!-- 待办事项内容 -->
        <view class="todo-content">
          <text class="todo-description">
            {{language === 'en_us' && item.descriptionEn ? item.descriptionEn : item.description}}
          </text>
          
          <view class="todo-meta">
            <view class="meta-item">
              <text class="meta-emoji">📂</text>
              <text class="meta-text">
                {{language === 'en_us' && item.categoryEn ? item.categoryEn : item.category}}
              </text>
            </view>
            <view class="meta-item">
              <text class="meta-emoji">🕒</text>
              <text class="meta-text">{{item.updatedAt}}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="todo-actions">
          <view class="action-button primary" bindtap="goToDetail" data-id="{{item.id}}">
            {{language === 'en_us' ? 'View Details' : '查看详情'}}
          </view>
        </view>

        <!-- 箭头指示器 -->
        <view class="todo-arrow">
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>
  </view>
</view>
