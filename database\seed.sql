-- 测试数据插入脚本
USE school_enrollment_todo;

-- 插入管理员用户
INSERT INTO users (openid, nickname, role, real_name, email, language_preference) VALUES
('admin_openid_001', 'Admin User', 'super_admin', '管理员', '<EMAIL>', 'zh_cn'),
('admin_openid_002', 'Teacher Zhang', 'admin', '张老师', '<EMAIL>', 'zh_cn'),
('admin_openid_003', '<PERSON>', 'admin', '<PERSON>', '<EMAIL>', 'en_us');

-- 插入测试学生
INSERT INTO users (openid, nickname, role, student_id, real_name, phone, email, language_preference) VALUES
('student_openid_001', '小明', 'student', 'STU2024001', '王小明', '13800138001', '<EMAIL>', 'zh_cn'),
('student_openid_002', '<PERSON>', 'student', 'STU2024002', '<PERSON>', '13800138002', '<EMAIL>', 'en_us'),
('student_openid_003', '小红', 'student', 'STU2024003', '李小红', '13800138003', '<EMAIL>', 'zh_cn'),
('student_openid_004', 'Emma', 'student', 'STU2024004', 'Emma Johnson', '13800138004', '<EMAIL>', 'en_us');

-- 插入待办事项模板
INSERT INTO todo_templates (title, title_en, description, description_en, category, priority, required, sort_order, form_config) VALUES
('身份验证', 'Identity Verification', '请上传身份证或护照照片进行身份验证', 'Please upload ID card or passport photo for identity verification', '报到登记', 3, TRUE, 1, 
'{"fields": [{"name": "id_type", "type": "select", "label": "证件类型", "label_en": "ID Type", "options": [{"value": "id_card", "label": "身份证", "label_en": "ID Card"}, {"value": "passport", "label": "护照", "label_en": "Passport"}], "required": true}, {"name": "id_photo", "type": "file", "label": "证件照片", "label_en": "ID Photo", "accept": "image/*", "required": true}]}'),

('学费缴纳', 'Tuition Payment', '请上传学费缴费凭证', 'Please upload tuition payment receipt', '财务缴费', 3, TRUE, 2,
'{"fields": [{"name": "payment_method", "type": "select", "label": "缴费方式", "label_en": "Payment Method", "options": [{"value": "bank_transfer", "label": "银行转账", "label_en": "Bank Transfer"}, {"value": "online_payment", "label": "在线支付", "label_en": "Online Payment"}], "required": true}, {"name": "receipt", "type": "file", "label": "缴费凭证", "label_en": "Payment Receipt", "accept": "image/*,application/pdf", "required": true}, {"name": "amount", "type": "number", "label": "缴费金额", "label_en": "Amount", "required": true}]}'),

('体检报告提交', 'Medical Report Submission', '请上传体检报告', 'Please upload medical examination report', '健康管理', 2, TRUE, 3,
'{"fields": [{"name": "hospital_name", "type": "text", "label": "体检医院", "label_en": "Hospital Name", "required": true}, {"name": "exam_date", "type": "date", "label": "体检日期", "label_en": "Examination Date", "required": true}, {"name": "report_file", "type": "file", "label": "体检报告", "label_en": "Medical Report", "accept": "application/pdf,image/*", "required": true}]}'),

('宿舍分配确认', 'Dormitory Assignment Confirmation', '请选择宿舍类型偏好', 'Please select dormitory type preference', '住宿安排', 2, TRUE, 4,
'{"fields": [{"name": "room_type", "type": "select", "label": "房间类型", "label_en": "Room Type", "options": [{"value": "single", "label": "单人间", "label_en": "Single Room"}, {"value": "double", "label": "双人间", "label_en": "Double Room"}, {"value": "quad", "label": "四人间", "label_en": "Quad Room"}], "required": true}, {"name": "special_requirements", "type": "textarea", "label": "特殊要求", "label_en": "Special Requirements", "required": false}]}'),

('课程选择', 'Course Selection', '请选择选修课程', 'Please select elective courses', '学务办理', 2, TRUE, 5,
'{"fields": [{"name": "elective_courses", "type": "select", "label": "选修课程", "label_en": "Elective Courses", "multiple": true, "options": [{"value": "art", "label": "美术", "label_en": "Art"}, {"value": "music", "label": "音乐", "label_en": "Music"}, {"value": "sports", "label": "体育", "label_en": "Sports"}, {"value": "computer", "label": "计算机", "label_en": "Computer Science"}], "required": true}]}'),

('签证信息确认', 'Visa Information Confirmation', '国际学生请上传签证信息', 'International students please upload visa information', '国际学生专项', 3, FALSE, 6,
'{"fields": [{"name": "visa_type", "type": "select", "label": "签证类型", "label_en": "Visa Type", "options": [{"value": "student", "label": "学生签证", "label_en": "Student Visa"}, {"value": "family", "label": "家属签证", "label_en": "Family Visa"}], "required": true}, {"name": "visa_photo", "type": "file", "label": "签证页面", "label_en": "Visa Page", "accept": "image/*", "required": true}, {"name": "expiry_date", "type": "date", "label": "签证到期日", "label_en": "Expiry Date", "required": true}]}');

-- 插入多语言配置
INSERT INTO language_configs (config_key, zh_cn, en_us, category) VALUES
('app.title', '入学待办事项', 'Enrollment To-Do', 'app'),
('app.welcome', '欢迎使用入学待办事项系统', 'Welcome to Enrollment To-Do System', 'app'),
('status.pending', '待处理', 'Pending', 'status'),
('status.processing', '进行中', 'Processing', 'status'),
('status.completed', '已完成', 'Completed', 'status'),
('status.need_supplement', '需补充', 'Need Supplement', 'status'),
('status.exception', '异常', 'Exception', 'status'),
('button.submit', '提交', 'Submit', 'button'),
('button.cancel', '取消', 'Cancel', 'button'),
('button.confirm', '确认', 'Confirm', 'button'),
('button.edit', '编辑', 'Edit', 'button'),
('button.delete', '删除', 'Delete', 'button'),
('message.success', '操作成功', 'Operation Successful', 'message'),
('message.error', '操作失败', 'Operation Failed', 'message'),
('message.loading', '加载中...', 'Loading...', 'message');

-- 为测试学生创建待办事项
INSERT INTO student_todos (student_id, template_id, status) 
SELECT u.id, t.id, 'pending'
FROM users u 
CROSS JOIN todo_templates t 
WHERE u.role = 'student';

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('file_upload_max_size', '10485760', '文件上传最大大小（字节）'),
('supported_file_types', 'jpg,jpeg,png,pdf,doc,docx', '支持的文件类型'),
('default_language', 'zh_cn', '默认语言'),
('app_name', '入学待办事项系统', '应用名称'),
('contact_email', '<EMAIL>', '联系邮箱'),
('contact_phone', '************', '联系电话');
