# 线下入学待办事项小程序 - 数据库设置脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "线下入学待办事项小程序 - 数据库设置" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# MySQL路径
$mysqlPath = "C:\Program Files\MySQL\MySQL Server 9.3\bin\mysql.exe"

# 检查MySQL是否存在
if (-not (Test-Path $mysqlPath)) {
    Write-Host "❌ 找不到MySQL，请确保MySQL已正确安装" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host "请输入MySQL root用户的密码：" -ForegroundColor Yellow
$password = Read-Host -AsSecureString
$plainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))

Write-Host ""
Write-Host "正在创建数据库..." -ForegroundColor Green

# 创建数据库
$initSqlPath = "database\init.sql"
if (Test-Path $initSqlPath) {
    & $mysqlPath -u root "-p$plainPassword" -e "source $initSqlPath"

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 数据库创建成功！" -ForegroundColor Green
        Write-Host ""

        $choice = Read-Host "是否要插入测试数据？ (Y/N)"
        if ($choice -eq "Y" -or $choice -eq "y") {
            Write-Host "正在插入测试数据..." -ForegroundColor Green
            $seedSqlPath = "database\seed.sql"
            if (Test-Path $seedSqlPath) {
                & $mysqlPath -u root "-p$plainPassword" -e "source $seedSqlPath"
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✅ 测试数据插入成功！" -ForegroundColor Green
                } else {
                    Write-Host "❌ 测试数据插入失败" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ 找不到测试数据文件：$seedSqlPath" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "❌ 数据库创建失败，请检查密码是否正确" -ForegroundColor Red
    }
} else {
    Write-Host "❌ 找不到数据库初始化文件：$initSqlPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "现在可以启动后端服务了！" -ForegroundColor Cyan
Write-Host "运行命令：cd backend; npm run dev" -ForegroundColor Yellow
Write-Host ""
Read-Host "按Enter键退出"
