/* pages/test/test.wxss */
.container {
  padding: 32rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 48rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.status {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.section {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 24rpx;
  display: block;
}

.test-item {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.counter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.list-item {
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #1976d2;
}

.buttons {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-primary {
  background-color: #1976d2;
  color: white;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}
