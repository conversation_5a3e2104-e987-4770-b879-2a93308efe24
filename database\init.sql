-- 线下入学待办事项小程序数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS school_enrollment_todo 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE school_enrollment_todo;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
    unionid VARCHAR(100) COMMENT '微信unionid',
    nickname VARCHAR(50) COMMENT '微信昵称',
    avatar_url VARCHAR(200) COMMENT '头像URL',
    role ENUM('student', 'admin', 'super_admin') DEFAULT 'student' COMMENT '用户角色',
    student_id VARCHAR(20) UNIQUE COMMENT '学号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    language_preference ENUM('zh_cn', 'en_us') DEFAULT 'zh_cn' COMMENT '语言偏好',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_openid (openid),
    INDEX idx_student_id (student_id),
    INDEX idx_role (role)
) COMMENT='用户表';

-- 待办事项模板表
CREATE TABLE todo_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL COMMENT '标题（中文）',
    title_en VARCHAR(100) COMMENT '标题（英文）',
    description TEXT COMMENT '描述（中文）',
    description_en TEXT COMMENT '描述（英文）',
    category VARCHAR(50) COMMENT '分类',
    priority INT DEFAULT 1 COMMENT '优先级：1-低，2-中，3-高',
    required BOOLEAN DEFAULT TRUE COMMENT '是否必需',
    sort_order INT DEFAULT 0 COMMENT '排序',
    form_config JSON COMMENT '自定义表单配置',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_by INT COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) COMMENT='待办事项模板表';

-- 学生待办状态表
CREATE TABLE student_todos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL COMMENT '学生ID',
    template_id INT NOT NULL COMMENT '模板ID',
    status ENUM('pending', 'processing', 'completed', 'need_supplement', 'exception') DEFAULT 'pending' COMMENT '状态',
    operator_id INT COMMENT '操作员ID',
    remark TEXT COMMENT '备注',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id),
    FOREIGN KEY (template_id) REFERENCES todo_templates(id),
    FOREIGN KEY (operator_id) REFERENCES users(id),
    UNIQUE KEY unique_student_template (student_id, template_id),
    INDEX idx_student_id (student_id),
    INDEX idx_template_id (template_id),
    INDEX idx_status (status)
) COMMENT='学生待办状态表';

-- 学生提交数据表
CREATE TABLE student_submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_todo_id INT NOT NULL COMMENT '学生待办ID',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名',
    field_type ENUM('text', 'select', 'file', 'date', 'number', 'textarea') NOT NULL COMMENT '字段类型',
    field_value TEXT COMMENT '字段值',
    file_url VARCHAR(500) COMMENT '文件URL',
    file_name VARCHAR(200) COMMENT '文件名',
    file_size INT COMMENT '文件大小（字节）',
    file_type VARCHAR(50) COMMENT '文件类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_todo_id) REFERENCES student_todos(id) ON DELETE CASCADE,
    INDEX idx_student_todo_id (student_todo_id),
    INDEX idx_field_name (field_name)
) COMMENT='学生提交数据表';

-- 多语言配置表
CREATE TABLE language_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    zh_cn TEXT COMMENT '中文内容',
    en_us TEXT COMMENT '英文内容',
    category VARCHAR(50) DEFAULT 'general' COMMENT '分类',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_key (config_key),
    INDEX idx_category (category)
) COMMENT='多语言配置表';

-- 操作日志表
CREATE TABLE operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL COMMENT '学生ID',
    todo_id INT NOT NULL COMMENT '待办事项ID',
    operator_id INT NOT NULL COMMENT '操作员ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    old_status VARCHAR(20) COMMENT '原状态',
    new_status VARCHAR(20) COMMENT '新状态',
    remark TEXT COMMENT '备注',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id),
    FOREIGN KEY (todo_id) REFERENCES student_todos(id),
    FOREIGN KEY (operator_id) REFERENCES users(id),
    INDEX idx_student_id (student_id),
    INDEX idx_todo_id (todo_id),
    INDEX idx_operator_id (operator_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) COMMENT='操作日志表';

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(200) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_config_key (config_key)
) COMMENT='系统配置表';

