// pages/profile/profile.js
const app = getApp();
const { t } = require('../../utils/i18n');

Page({
  data: {
    userInfo: null,
    language: 'zh_cn',
    editing: false,
    formData: {
      realName: '',
      phone: '',
      email: '',
      languagePreference: 'zh_cn'
    }
  },

  onLoad() {
    console.log('个人中心页面加载');
    this.setData({
      language: app.globalData.language
    });
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: t('profile.title')
    });
    
    // 检查登录状态
    if (!app.globalData.token) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    
    this.loadUserInfo();
  },

  onShow() {
    // 每次显示页面时刷新用户信息
    if (app.globalData.token) {
      this.loadUserInfo();
    }
  },

  // 语言变更回调
  onLanguageChange(language) {
    this.setData({ language });
    wx.setNavigationBarTitle({
      title: t('profile.title')
    });
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 这里应该调用实际的API
      // const response = await app.request({
      //   url: '/auth/me'
      // });
      
      // 使用全局用户信息或模拟数据
      const userInfo = app.globalData.userInfo || {
        id: 1,
        nickname: '测试用户',
        realName: '张三',
        studentId: 'STU2024001',
        phone: '13800138001',
        email: '<EMAIL>',
        languagePreference: 'zh_cn',
        avatarUrl: '/images/default-avatar.png',
        role: 'student'
      };
      
      this.setData({
        userInfo,
        formData: {
          realName: userInfo.realName || '',
          phone: userInfo.phone || '',
          email: userInfo.email || '',
          languagePreference: userInfo.languagePreference || 'zh_cn'
        }
      });
      
    } catch (error) {
      console.error('获取用户信息失败:', error);
      wx.showToast({
        title: t('common.error'),
        icon: 'error'
      });
    }
  },

  // 切换编辑模式
  toggleEdit() {
    this.setData({ editing: !this.data.editing });
  },

  // 表单输入处理
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 语言选择
  onLanguageSelect(e) {
    const language = e.currentTarget.dataset.language;
    this.setData({
      'formData.languagePreference': language
    });
  },

  // 保存用户信息
  async saveUserInfo() {
    try {
      const { formData } = this.data;
      
      // 验证表单
      if (!formData.realName.trim()) {
        wx.showToast({
          title: this.data.language === 'en_us' ? 'Please enter real name' : '请输入真实姓名',
          icon: 'error'
        });
        return;
      }
      
      // 这里应该调用实际的API
      // const response = await app.request({
      //   url: '/auth/profile',
      //   method: 'PUT',
      //   data: formData
      // });
      
      // 模拟保存成功
      const updatedUserInfo = {
        ...this.data.userInfo,
        ...formData
      };
      
      // 更新全局用户信息
      app.globalData.userInfo = updatedUserInfo;
      wx.setStorageSync('userInfo', updatedUserInfo);
      
      // 如果语言发生变化，更新全局语言设置
      if (formData.languagePreference !== this.data.language) {
        app.setLanguage(formData.languagePreference);
      }
      
      this.setData({
        userInfo: updatedUserInfo,
        editing: false
      });
      
      wx.showToast({
        title: t('profile.save_success'),
        icon: 'success'
      });
      
    } catch (error) {
      console.error('保存用户信息失败:', error);
      wx.showToast({
        title: t('profile.save_failed'),
        icon: 'error'
      });
    }
  },

  // 取消编辑
  cancelEdit() {
    // 恢复原始数据
    const { userInfo } = this.data;
    this.setData({
      editing: false,
      formData: {
        realName: userInfo.realName || '',
        phone: userInfo.phone || '',
        email: userInfo.email || '',
        languagePreference: userInfo.languagePreference || 'zh_cn'
      }
    });
  },

  // 切换应用语言
  switchAppLanguage() {
    const currentLanguage = this.data.language;
    const newLanguage = currentLanguage === 'zh_cn' ? 'en_us' : 'zh_cn';
    
    app.setLanguage(newLanguage);
    this.setData({ 
      language: newLanguage,
      'formData.languagePreference': newLanguage
    });
    
    // 更新导航栏标题
    wx.setNavigationBarTitle({
      title: t('profile.title')
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: t('common.confirm'),
      content: this.data.language === 'en_us' ? 
        'Are you sure you want to logout?' : 
        '确定要退出登录吗？',
      confirmText: t('common.confirm'),
      cancelText: t('common.cancel'),
      success: (res) => {
        if (res.confirm) {
          app.logout();
        }
      }
    });
  },

  // 跳转到管理员页面（如果是管理员）
  goToAdmin() {
    if (this.data.userInfo.role === 'admin' || this.data.userInfo.role === 'super_admin') {
      wx.navigateTo({
        url: '/pages/admin/admin'
      });
    }
  }
});
