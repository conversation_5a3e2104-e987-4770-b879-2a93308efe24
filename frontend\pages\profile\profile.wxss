/* pages/profile/profile.wxss */

/* 个人资料头部 */
.profile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  margin-bottom: 24rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
}

.avatar-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

.user-basic-info {
  display: flex;
  flex-direction: column;
}

.user-nickname {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 4rpx;
}

.user-student-id {
  font-size: 24rpx;
  opacity: 0.8;
}

.language-switch-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.language-emoji {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.language-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 个人信息表单 */
.profile-form {
  margin-bottom: 24rpx;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.edit-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: var(--primary-color);
  color: white;
  border-radius: 24rpx;
  font-size: 26rpx;
}

.edit-emoji {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.edit-text {
  font-weight: 500;
}

.card-body {
  padding: 32rpx;
}

/* 表单组 */
.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid var(--divider-color);
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: var(--surface-color);
}

.form-input:focus {
  border-color: var(--primary-color);
}

.form-value {
  display: block;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: var(--text-secondary);
  border-bottom: 1rpx solid var(--divider-color);
}

/* 语言选项 */
.language-options {
  display: flex;
  gap: 16rpx;
}

.language-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx solid var(--divider-color);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.language-option.selected {
  border-color: var(--primary-color);
  background-color: rgba(25, 118, 210, 0.1);
}

.option-text {
  font-size: 28rpx;
  color: var(--text-primary);
}

.option-check {
  width: 32rpx;
  height: 32rpx;
  background-color: var(--primary-color);
  color: white;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
}

.form-actions .btn {
  flex: 1;
}

/* 功能菜单 */
.menu-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  transition: all 0.3s ease;
}

.menu-item:active {
  transform: scale(0.98);
  background-color: #f8f9fa;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.admin-icon {
  background-color: var(--primary-color);
}

.logout-icon {
  background-color: var(--error-color);
}

.menu-emoji {
  font-size: 48rpx;
  color: white;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.menu-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.logout-title {
  color: var(--error-color);
}

.menu-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.menu-arrow {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

/* 退出登录项特殊样式 */
.logout-item {
  border: 1rpx solid rgba(244, 67, 54, 0.2);
}

.logout-item:active {
  background-color: rgba(244, 67, 54, 0.05);
}
