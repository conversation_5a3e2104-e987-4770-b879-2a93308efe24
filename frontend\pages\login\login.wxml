<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 背景装饰 -->
  <view class="background-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <!-- 主要内容 -->
  <view class="login-content">
    <!-- Logo和标题 -->
    <view class="header-section">
      <view class="logo-container">
        <view class="logo-placeholder">📚</view>
      </view>
      <view class="title-container">
        <text class="app-title">
          {{language === 'en_us' ? 'Enrollment To-Do' : '入学待办事项'}}
        </text>
        <text class="app-subtitle">
          {{language === 'en_us' ? 'Welcome to Enrollment To-Do System' : '欢迎使用入学待办事项系统'}}
        </text>
      </view>
    </view>

    <!-- 功能介绍 -->
    <view class="features-section">
      <view class="feature-item">
        <view class="feature-icon">
          <text class="feature-emoji">✅</text>
        </view>
        <view class="feature-text">
          <text class="feature-title">
            {{language === 'en_us' ? 'Smart To-Do Management' : '智能待办管理'}}
          </text>
          <text class="feature-desc">
            {{language === 'en_us' ? 'Track your enrollment progress easily' : '轻松跟踪入学进度'}}
          </text>
        </view>
      </view>

      <view class="feature-item">
        <view class="feature-icon">
          <text class="feature-emoji">⚡</text>
        </view>
        <view class="feature-text">
          <text class="feature-title">
            {{language === 'en_us' ? 'Real-time Updates' : '实时更新'}}
          </text>
          <text class="feature-desc">
            {{language === 'en_us' ? 'Get instant notifications on status changes' : '状态变更即时通知'}}
          </text>
        </view>
      </view>

      <view class="feature-item">
        <view class="feature-icon">
          <text class="feature-emoji">🌐</text>
        </view>
        <view class="feature-text">
          <text class="feature-title">
            {{language === 'en_us' ? 'Bilingual Support' : '双语支持'}}
          </text>
          <text class="feature-desc">
            {{language === 'en_us' ? 'Chinese and English interface' : '中英文界面切换'}}
          </text>
        </view>
      </view>
    </view>

    <!-- 登录按钮 -->
    <view class="login-section">
      <button 
        class="login-button {{loading ? 'loading' : ''}}"
        bindtap="handleWechatLogin"
        disabled="{{loading}}"
      >
        <view wx:if="{{loading}}" class="button-loading">
          <view class="loading-spinner"></view>
          <text class="loading-text">
            {{language === 'en_us' ? 'Logging in...' : '登录中...'}}
          </text>
        </view>
        <view wx:else class="button-content">
          <text class="wechat-emoji">💬</text>
          <text class="button-text">
            {{language === 'en_us' ? 'Login with WeChat' : '微信一键登录'}}
          </text>
        </view>
      </button>

      <!-- 测试登录按钮（开发环境） -->
      <button
        class="test-login-button"
        bindtap="handleTestLogin"
        disabled="{{loading}}"
      >
        <text class="button-text">
          {{language === 'en_us' ? 'Test Login (Dev)' : '测试登录（开发）'}}
        </text>
      </button>

      <!-- 语言切换 -->
      <view class="language-switch" bindtap="switchLanguage">
        <text class="language-emoji">🌍</text>
        <text class="language-text">
          {{language === 'en_us' ? '中文' : 'English'}}
        </text>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer-section">
      <text class="footer-text">
        {{language === 'en_us' ? 
          'By logging in, you agree to our Terms of Service and Privacy Policy' : 
          '登录即表示您同意我们的服务条款和隐私政策'}}
      </text>
    </view>
  </view>
</view>
