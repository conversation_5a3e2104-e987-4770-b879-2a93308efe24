// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    language: 'zh_cn', // 默认语言
    baseUrl: 'http://localhost:3000/api', // 后端API地址
    socketUrl: 'http://localhost:3000' // Socket.IO地址
  },

  onLaunch() {
    console.log('小程序启动');
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 获取系统语言设置
    this.getSystemLanguage();
  },

  onShow() {
    console.log('小程序显示');
  },

  onHide() {
    console.log('小程序隐藏');
  },

  onError(msg) {
    console.error('小程序错误:', msg);
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      console.log('用户已登录:', userInfo);
    } else {
      console.log('用户未登录');
    }
  },

  // 获取系统语言设置
  getSystemLanguage() {
    try {
      // 使用新的API替代废弃的wx.getSystemInfoSync
      const systemInfo = wx.getAppBaseInfo();
      const language = systemInfo.language || 'zh_cn';

      // 根据系统语言设置默认语言
      if (language.includes('en')) {
        this.globalData.language = 'en_us';
      } else {
        this.globalData.language = 'zh_cn';
      }

      // 从本地存储获取用户设置的语言
      const savedLanguage = wx.getStorageSync('language');
      if (savedLanguage) {
        this.globalData.language = savedLanguage;
      }

      console.log('当前语言:', this.globalData.language);
    } catch (error) {
      console.error('获取系统信息失败:', error);
      // 如果新API不可用，使用默认值
      this.globalData.language = 'zh_cn';
    }
  },

  // 设置语言
  setLanguage(language) {
    this.globalData.language = language;
    wx.setStorageSync('language', language);
    
    // 通知所有页面语言已更改
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.onLanguageChange && typeof page.onLanguageChange === 'function') {
        page.onLanguageChange(language);
      }
    });
  },

  // 微信登录
  async wechatLogin() {
    try {
      // 获取微信授权码
      const loginRes = await this.promisify(wx.login)();
      const code = loginRes.code;
      
      if (!code) {
        throw new Error('获取微信授权码失败');
      }
      
      // 获取用户信息
      const userInfoRes = await this.promisify(wx.getUserProfile)({
        desc: '用于完善用户资料'
      });
      
      // 调用后端登录接口
      const response = await this.request({
        url: '/auth/wechat-login',
        method: 'POST',
        data: {
          code: code,
          userInfo: userInfoRes.userInfo
        }
      });
      
      // 保存登录信息
      this.globalData.token = response.token;
      this.globalData.userInfo = response.user;
      
      wx.setStorageSync('token', response.token);
      wx.setStorageSync('userInfo', response.user);
      
      console.log('登录成功:', response.user);
      return response;
      
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      });
      throw error;
    }
  },

  // 退出登录
  logout() {
    this.globalData.token = null;
    this.globalData.userInfo = null;
    
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': this.globalData.token ? `Bearer ${this.globalData.token}` : '',
          ...options.header
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data);
          } else if (res.statusCode === 401) {
            // token过期，重新登录
            this.logout();
            reject(new Error('登录已过期'));
          } else {
            reject(new Error(res.data.error || '请求失败'));
          }
        },
        fail: (error) => {
          console.error('网络请求失败:', error);
          reject(new Error('网络连接失败'));
        }
      });
    });
  },

  // Promise化微信API
  promisify(fn) {
    return (options = {}) => {
      return new Promise((resolve, reject) => {
        fn({
          ...options,
          success: resolve,
          fail: reject
        });
      });
    };
  }
});
