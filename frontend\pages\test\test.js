// pages/test/test.js
Page({
  data: {
    message: '小程序测试页面',
    count: 0,
    isLoaded: false,
    testData: {
      title: '测试标题',
      content: '这是一个测试内容',
      items: [
        { id: 1, name: '测试项目1' },
        { id: 2, name: '测试项目2' },
        { id: 3, name: '测试项目3' }
      ]
    }
  },

  onLoad() {
    console.log('测试页面开始加载');
    try {
      wx.setNavigationBarTitle({
        title: '测试页面'
      });

      this.setData({
        isLoaded: true
      });

      console.log('测试页面加载完成');
    } catch (error) {
      console.error('测试页面加载失败:', error);
    }
  },

  onShow() {
    console.log('测试页面显示');
  },

  // 点击计数器
  onTapCounter() {
    this.setData({
      count: this.data.count + 1
    });
    console.log('计数器点击，当前值:', this.data.count);
  },

  // 显示提示
  showToast() {
    wx.showToast({
      title: '测试成功！',
      icon: 'success',
      duration: 2000
    });
  },

  // 跳转到首页
  goToIndex() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 测试网络请求
  testNetwork() {
    console.log('开始测试网络请求');
    wx.request({
      url: 'http://localhost:3000/health',
      method: 'GET',
      success: (res) => {
        console.log('网络请求成功:', res.data);
        wx.showModal({
          title: '网络测试成功',
          content: `服务器状态: ${res.data.status}\n环境: ${res.data.environment}`,
          showCancel: false
        });
      },
      fail: (err) => {
        console.error('网络请求失败:', err);
        wx.showModal({
          title: '网络测试失败',
          content: `错误信息: ${err.errMsg}`,
          showCancel: false
        });
      }
    });
  }
});
