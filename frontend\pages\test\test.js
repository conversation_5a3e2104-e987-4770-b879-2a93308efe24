// pages/test/test.js
Page({
  data: {
    message: '小程序测试页面',
    count: 0,
    testData: {
      title: '测试标题',
      content: '这是一个测试内容',
      items: [
        { id: 1, name: '测试项目1' },
        { id: 2, name: '测试项目2' },
        { id: 3, name: '测试项目3' }
      ]
    }
  },

  onLoad() {
    console.log('测试页面加载');
    wx.setNavigationBarTitle({
      title: '测试页面'
    });
  },

  onShow() {
    console.log('测试页面显示');
  },

  // 点击计数器
  onTapCounter() {
    this.setData({
      count: this.data.count + 1
    });
    console.log('计数器点击，当前值:', this.data.count);
  },

  // 显示提示
  showToast() {
    wx.showToast({
      title: '测试成功！',
      icon: 'success',
      duration: 2000
    });
  },

  // 跳转到首页
  goToIndex() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
