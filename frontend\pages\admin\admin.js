// pages/admin/admin.js
const app = getApp();
const { t } = require('../../utils/i18n');

Page({
  data: {
    language: 'zh_cn',
    userInfo: null
  },

  onLoad() {
    console.log('管理员页面加载');
    this.setData({
      language: app.globalData.language,
      userInfo: app.globalData.userInfo
    });
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: t('admin.title')
    });
    
    // 检查权限
    if (!this.checkAdminPermission()) {
      wx.showModal({
        title: t('common.error'),
        content: t('error.forbidden'),
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
  },

  // 检查管理员权限
  checkAdminPermission() {
    const userInfo = app.globalData.userInfo;
    return userInfo && (userInfo.role === 'admin' || userInfo.role === 'super_admin');
  },

  // 语言变更回调
  onLanguageChange(language) {
    this.setData({ language });
    wx.setNavigationBarTitle({
      title: t('admin.title')
    });
  }
});
