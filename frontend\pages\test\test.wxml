<!--pages/test/test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">{{message}}</text>
    <text class="status">{{isLoaded ? '✅ 页面加载成功' : '⏳ 页面加载中...'}}</text>
  </view>

  <view wx:if="{{isLoaded}}" class="content">
    <view class="section">
      <text class="section-title">基础测试</text>
      <view class="test-item">
        <text>标题: {{testData.title}}</text>
      </view>
      <view class="test-item">
        <text>内容: {{testData.content}}</text>
      </view>
    </view>

    <view class="section">
      <text class="section-title">交互测试</text>
      <view class="counter">
        <text>点击次数: {{count}}</text>
        <button class="btn" bindtap="onTapCounter">点击我</button>
      </view>
    </view>

    <view class="section">
      <text class="section-title">列表测试</text>
      <view class="list">
        <view wx:for="{{testData.items}}" wx:key="id" class="list-item">
          <text>{{item.name}}</text>
        </view>
      </view>
    </view>

    <view class="section">
      <text class="section-title">功能测试</text>
      <view class="buttons">
        <button class="btn btn-primary" bindtap="showToast">显示提示</button>
        <button class="btn btn-success" bindtap="testNetwork">测试网络</button>
        <button class="btn btn-secondary" bindtap="goToIndex">返回首页</button>
      </view>
    </view>
  </view>
</view>
