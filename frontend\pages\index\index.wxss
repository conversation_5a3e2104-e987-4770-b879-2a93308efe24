/* pages/index/index.wxss */

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-text {
  margin-top: 32rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 32rpx;
}

.welcome-card {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  display: block;
  font-size: 32rpx;
  font-weight: 300;
  margin-bottom: 8rpx;
}

.welcome-name {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
}

.welcome-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 60rpx;
}

/* 进度区域 */
.progress-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.progress-bar-container {
  display: flex;
  align-items: center;
  margin: 32rpx 0;
}

.progress-bar {
  flex: 1;
  height: 16rpx;
  background-color: var(--divider-color);
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 24rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-color);
  min-width: 80rpx;
  text-align: right;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: 8rpx;
}

/* 最近待办区域 */
.recent-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-action {
  font-size: 28rpx;
  color: var(--primary-color);
  font-weight: 500;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-emoji {
  font-size: 120rpx;
  opacity: 0.5;
  margin-bottom: 32rpx;
}

.empty-text {
  color: var(--text-secondary);
  font-size: 28rpx;
}

.todos-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  transition: all 0.3s ease;
}

.todo-item:active {
  transform: scale(0.98);
  background-color: #f8f9fa;
}

.todo-content {
  flex: 1;
}

.todo-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.todo-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
  margin-right: 16rpx;
}

.todo-meta {
  display: flex;
  align-items: center;
}

.todo-time {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.todo-arrow {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

/* 快捷操作区域 */
.actions-section {
  margin-bottom: 32rpx;
}

.actions-grid {
  display: flex;
  gap: 24rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48rpx 32rpx;
  background-color: var(--surface-color);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 24rpx;
  border-radius: 40rpx;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-emoji {
  font-size: 48rpx;
  color: white;
}

.action-text {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
  text-align: center;
}
