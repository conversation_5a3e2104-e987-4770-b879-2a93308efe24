@echo off
echo ========================================
echo 线下入学待办事项小程序 - 数据库设置
echo ========================================
echo.

echo 请确保MySQL服务正在运行...
echo.

echo 正在连接到MySQL数据库...
echo 请输入MySQL root用户的密码：
echo.

"C:\Program Files\MySQL\MySQL Server 9.3\bin\mysql.exe" -u root -p < database\init.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 数据库创建成功！
    echo.
    echo 是否要插入测试数据？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo 正在插入测试数据...
        "C:\Program Files\MySQL\MySQL Server 9.3\bin\mysql.exe" -u root -p < database\seed.sql
        if %ERRORLEVEL% EQU 0 (
            echo ✅ 测试数据插入成功！
        ) else (
            echo ❌ 测试数据插入失败
        )
    )
) else (
    echo ❌ 数据库创建失败，请检查MySQL是否正在运行
)

echo.
echo 按任意键继续...
pause > nul
