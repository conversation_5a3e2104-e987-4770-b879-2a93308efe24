/* pages/todos/todos.wxss */

/* 搜索和筛选区域 */
.search-filter-section {
  background-color: var(--surface-color);
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-container {
  position: relative;
  margin-bottom: 24rpx;
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 48rpx 0 24rpx;
  border: 2rpx solid var(--divider-color);
  border-radius: 40rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
}

.search-input:focus {
  border-color: var(--primary-color);
  background-color: var(--surface-color);
}

.search-emoji {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  opacity: 0.6;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  gap: 16rpx;
}

.filter-tab {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2rpx solid var(--divider-color);
  color: var(--text-secondary);
  background-color: var(--surface-color);
}

.filter-tab.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.filter-tab:not(.active):active {
  background-color: #f0f0f0;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.loading-text {
  margin-top: 32rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 待办事项容器 */
.todos-container {
  padding: 0 32rpx 32rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-emoji {
  font-size: 120rpx;
  opacity: 0.5;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* 待办事项列表 */
.todos-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.todo-item {
  position: relative;
  padding: 32rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}

.todo-item:active {
  transform: scale(0.98);
  background-color: #f8f9fa;
}

/* 待办事项头部 */
.todo-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.todo-title-section {
  flex: 1;
  margin-right: 16rpx;
}

.todo-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: block;
}

.todo-badges {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.badge {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.required-badge {
  background-color: #fff3e0;
  color: #e65100;
}

.priority-badge {
  color: white;
}

.priority-1 {
  background-color: #4caf50;
}

.priority-2 {
  background-color: #ff9800;
}

.priority-3 {
  background-color: #f44336;
}

/* 待办事项内容 */
.todo-content {
  margin-bottom: 24rpx;
}

.todo-description {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: block;
}

.todo-meta {
  display: flex;
  gap: 32rpx;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-emoji {
  font-size: 24rpx;
  opacity: 0.6;
  margin-right: 8rpx;
}

.meta-text {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 操作按钮 */
.todo-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.action-button {
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.action-button.primary {
  background-color: var(--primary-color);
  color: white;
}

.action-button.primary:active {
  background-color: var(--primary-dark);
}

/* 箭头指示器 */
.todo-arrow {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
}

.arrow-icon {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

/* 状态样式 */
.status-pending {
  background-color: #fff3e0;
  color: #e65100;
}

.status-processing {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.status-completed {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-need-supplement {
  background-color: #fff8e1;
  color: #f57c00;
}

.status-exception {
  background-color: #ffebee;
  color: #c62828;
}
