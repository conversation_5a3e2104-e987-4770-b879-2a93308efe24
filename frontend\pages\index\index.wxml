<!--pages/index/index.wxml-->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{language === 'en_us' ? 'Loading...' : '加载中...'}}</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="container">
    <!-- 欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-card card">
        <view class="welcome-content">
          <view class="welcome-text">
            <text class="welcome-title">
              {{language === 'en_us' ? 'Welcome' : '欢迎'}}
            </text>
            <text class="welcome-name">{{userInfo.realName || userInfo.nickname || 'User'}}</text>
          </view>
          <view class="welcome-avatar" bindtap="goToProfile">
            <view class="avatar-placeholder">👤</view>
          </view>
        </view>

        <!-- 未登录时显示登录按钮 -->
        <view wx:if="{{!userInfo.id}}" class="login-prompt">
          <button class="login-btn" bindtap="goToLogin">
            {{language === 'en_us' ? 'Login' : '点击登录'}}
          </button>
        </view>
      </view>
    </view>

    <!-- 进度概览 -->
    <view class="progress-section">
      <view class="progress-card card">
        <view class="card-header">
          <text class="section-title">
            {{language === 'en_us' ? 'Overall Progress' : '整体进度'}}
          </text>
        </view>
        <view class="card-body">
          <!-- 进度条 -->
          <view class="progress-bar-container">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{progress.percentage}}%"></view>
            </view>
            <text class="progress-text">{{progress.percentage}}%</text>
          </view>
          
          <!-- 统计数据 -->
          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-number">{{progress.total}}</text>
              <text class="stat-label">
                {{language === 'en_us' ? 'Total' : '总计'}}
              </text>
            </view>
            <view class="stat-item">
              <text class="stat-number text-success">{{progress.completed}}</text>
              <text class="stat-label">
                {{language === 'en_us' ? 'Completed' : '已完成'}}
              </text>
            </view>
            <view class="stat-item">
              <text class="stat-number text-warning">{{progress.pending}}</text>
              <text class="stat-label">
                {{language === 'en_us' ? 'Pending' : '待处理'}}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近待办事项 -->
    <view class="recent-section">
      <view class="section-header">
        <text class="section-title">
          {{language === 'en_us' ? 'Recent To-Dos' : '最近待办'}}
        </text>
        <text class="section-action" bindtap="goToTodos">
          {{language === 'en_us' ? 'View All' : '查看全部'}}
        </text>
      </view>

      <view wx:if="{{recentTodos.length === 0}}" class="empty-state">
        <view class="empty-emoji">📝</view>
        <text class="empty-text">
          {{language === 'en_us' ? 'No recent to-dos' : '暂无最近待办'}}
        </text>
      </view>

      <view wx:else class="todos-list">
        <view 
          wx:for="{{recentTodos}}" 
          wx:key="id" 
          class="todo-item card"
          bindtap="goToTodoDetail"
          data-id="{{item.id}}"
        >
          <view class="todo-content">
            <view class="todo-header">
              <text class="todo-title">
                {{language === 'en_us' && item.titleEn ? item.titleEn : item.title}}
              </text>
              <view class="status-badge status-{{item.status}}">
                {{language === 'en_us' ? 
                  (item.status === 'pending' ? 'Pending' : 
                   item.status === 'processing' ? 'Processing' : 
                   item.status === 'completed' ? 'Completed' : 
                   item.status === 'need_supplement' ? 'Need Supplement' : 'Exception') :
                  (item.status === 'pending' ? '待处理' : 
                   item.status === 'processing' ? '进行中' : 
                   item.status === 'completed' ? '已完成' : 
                   item.status === 'need_supplement' ? '需补充' : '异常')
                }}
              </view>
            </view>
            <view class="todo-meta">
              <text class="todo-time">{{item.updatedAt}}</text>
            </view>
          </view>
          <view class="todo-arrow">
            <text class="arrow-icon">></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="actions-section">
      <view class="actions-grid">
        <view class="action-item" bindtap="goToTodos">
          <view class="action-icon">
            <text class="action-emoji">📋</text>
          </view>
          <text class="action-text">
            {{language === 'en_us' ? 'To-Do Items' : '待办事项'}}
          </text>
        </view>
        <view class="action-item" bindtap="goToProfile">
          <view class="action-icon">
            <text class="action-emoji">👤</text>
          </view>
          <text class="action-text">
            {{language === 'en_us' ? 'Profile' : '个人中心'}}
          </text>
        </view>
      </view>
    </view>
  </view>
</view>
