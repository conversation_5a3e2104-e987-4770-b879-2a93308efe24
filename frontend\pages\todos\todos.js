// pages/todos/todos.js
const app = getApp();
const { t } = require('../../utils/i18n');

Page({
  data: {
    todos: [],
    filteredTodos: [],
    loading: true,
    refreshing: false,
    currentFilter: 'all', // all, pending, completed
    searchKeyword: '',
    language: 'zh_cn',
    categories: []
  },

  onLoad() {
    console.log('待办事项页面加载');
    this.setData({
      language: app.globalData.language
    });
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: t('todos.title')
    });
    
    // 检查登录状态
    if (!app.globalData.token) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    
    this.loadTodos();
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (app.globalData.token) {
      this.loadTodos();
    }
  },

  onPullDownRefresh() {
    this.loadTodos().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 语言变更回调
  onLanguageChange(language) {
    this.setData({ language });
    wx.setNavigationBarTitle({
      title: t('todos.title')
    });
  },

  // 加载待办事项
  async loadTodos() {
    try {
      this.setData({ loading: true });
      
      // 这里应该调用实际的API
      // const response = await app.request({
      //   url: '/todos/student'
      // });
      
      // 模拟数据
      const mockTodos = [
        {
          id: 1,
          title: '身份验证',
          titleEn: 'Identity Verification',
          description: '请上传身份证或护照照片进行身份验证',
          descriptionEn: 'Please upload ID card or passport photo for identity verification',
          category: '报到登记',
          categoryEn: 'Registration',
          status: 'completed',
          priority: 3,
          required: true,
          updatedAt: '2024-12-01 10:30:00',
          completedAt: '2024-12-01 10:30:00'
        },
        {
          id: 2,
          title: '学费缴纳',
          titleEn: 'Tuition Payment',
          description: '请上传学费缴费凭证',
          descriptionEn: 'Please upload tuition payment receipt',
          category: '财务缴费',
          categoryEn: 'Financial Payment',
          status: 'pending',
          priority: 3,
          required: true,
          updatedAt: '2024-12-01 09:15:00'
        },
        {
          id: 3,
          title: '体检报告提交',
          titleEn: 'Medical Report Submission',
          description: '请上传体检报告',
          descriptionEn: 'Please upload medical examination report',
          category: '健康管理',
          categoryEn: 'Health Management',
          status: 'processing',
          priority: 2,
          required: true,
          updatedAt: '2024-11-30 16:45:00'
        },
        {
          id: 4,
          title: '宿舍分配确认',
          titleEn: 'Dormitory Assignment Confirmation',
          description: '请选择宿舍类型偏好',
          descriptionEn: 'Please select dormitory type preference',
          category: '住宿安排',
          categoryEn: 'Accommodation',
          status: 'need_supplement',
          priority: 2,
          required: true,
          updatedAt: '2024-11-29 14:20:00'
        },
        {
          id: 5,
          title: '课程选择',
          titleEn: 'Course Selection',
          description: '请选择选修课程',
          descriptionEn: 'Please select elective courses',
          category: '学务办理',
          categoryEn: 'Academic Affairs',
          status: 'pending',
          priority: 2,
          required: true,
          updatedAt: '2024-11-28 11:10:00'
        },
        {
          id: 6,
          title: '签证信息确认',
          titleEn: 'Visa Information Confirmation',
          description: '国际学生请上传签证信息',
          descriptionEn: 'International students please upload visa information',
          category: '国际学生专项',
          categoryEn: 'International Student',
          status: 'pending',
          priority: 1,
          required: false,
          updatedAt: '2024-11-27 09:30:00'
        }
      ];
      
      // 提取分类
      const categories = [...new Set(mockTodos.map(todo => 
        this.data.language === 'en_us' && todo.categoryEn ? todo.categoryEn : todo.category
      ))];
      
      this.setData({
        todos: mockTodos,
        categories,
        loading: false
      });
      
      // 应用当前筛选
      this.applyFilter();
      
    } catch (error) {
      console.error('加载待办事项失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: t('common.error'),
        icon: 'error'
      });
    }
  },

  // 筛选切换
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({ currentFilter: filter });
    this.applyFilter();
  },

  // 搜索
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({ searchKeyword: keyword });
    this.applyFilter();
  },

  // 应用筛选
  applyFilter() {
    const { todos, currentFilter, searchKeyword, language } = this.data;
    let filtered = [...todos];
    
    // 状态筛选
    if (currentFilter !== 'all') {
      filtered = filtered.filter(todo => todo.status === currentFilter);
    }
    
    // 搜索筛选
    if (searchKeyword) {
      filtered = filtered.filter(todo => {
        const title = language === 'en_us' && todo.titleEn ? todo.titleEn : todo.title;
        const description = language === 'en_us' && todo.descriptionEn ? todo.descriptionEn : todo.description;
        return title.toLowerCase().includes(searchKeyword.toLowerCase()) ||
               description.toLowerCase().includes(searchKeyword.toLowerCase());
      });
    }
    
    this.setData({ filteredTodos: filtered });
  },

  // 跳转到详情页
  goToDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/todo-detail/todo-detail?id=${id}`
    });
  },

  // 获取本地化标题
  getLocalizedTitle(item) {
    return this.data.language === 'en_us' && item.titleEn ? item.titleEn : item.title;
  },

  // 获取本地化描述
  getLocalizedDescription(item) {
    return this.data.language === 'en_us' && item.descriptionEn ? item.descriptionEn : item.description;
  },

  // 获取本地化分类
  getLocalizedCategory(item) {
    return this.data.language === 'en_us' && item.categoryEn ? item.categoryEn : item.category;
  },

  // 获取状态文本
  getStatusText(status) {
    return t(`status.${status}`);
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      zh_cn: { 1: '低', 2: '中', 3: '高' },
      en_us: { 1: 'Low', 2: 'Medium', 3: 'High' }
    };
    return priorityMap[this.data.language][priority] || '';
  },

  // 格式化时间
  formatTime(timeString) {
    const date = new Date(timeString);
    return date.toLocaleDateString();
  }
});
