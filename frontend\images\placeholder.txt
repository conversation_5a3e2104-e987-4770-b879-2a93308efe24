# 图标文件说明

这个目录用于存放小程序的图标文件。

## 需要的图标文件：

### 底部导航栏图标：
- home.png - 首页图标（未选中）
- home-active.png - 首页图标（选中）
- todo.png - 待办事项图标（未选中）
- todo-active.png - 待办事项图标（选中）
- profile.png - 个人中心图标（未选中）
- profile-active.png - 个人中心图标（选中）

### 其他图标：
- logo.png - 应用Logo
- default-avatar.png - 默认头像
- search-icon.png - 搜索图标
- edit-icon.png - 编辑图标
- language-icon.png - 语言切换图标
- admin-icon.png - 管理员图标
- logout-icon.png - 退出登录图标

## 图标规格：
- 底部导航栏图标：81px × 81px
- 其他图标：根据使用场景调整大小

## 临时解决方案：
目前已移除app.json中的图标配置，小程序可以正常运行。
后续可以添加实际的图标文件并恢复图标配置。
