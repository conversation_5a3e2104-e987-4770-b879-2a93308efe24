// pages/index/index.js
const app = getApp();

// 简化的翻译函数
function t(key) {
  const translations = {
    'common.error': '错误',
    'common.minutes_ago': '分钟前',
    'common.hours_ago': '小时前',
    'home.title': '入学待办事项'
  };
  return translations[key] || key;
}

Page({
  data: {
    userInfo: null,
    progress: {
      total: 0,
      completed: 0,
      pending: 0,
      percentage: 0
    },
    recentTodos: [],
    loading: true,
    language: 'zh_cn'
  },

  onLoad() {
    console.log('首页加载');
    this.setData({
      language: app.globalData.language || 'zh_cn'
    });

    // 直接加载测试数据，确保界面能显示
    console.log('加载测试数据');
    this.loadTestData();
  },

  onShow() {
    console.log('首页显示');
    // 简化逻辑，确保页面能正常显示
  },

  onPullDownRefresh() {
    this.loadData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 语言变更回调
  onLanguageChange(language) {
    this.setData({ language });
    wx.setNavigationBarTitle({
      title: t('home.title')
    });
  },

  // 加载测试数据（未登录时使用）
  loadTestData() {
    console.log('加载测试数据');

    const testUserInfo = {
      id: 1,
      nickname: '测试用户',
      realName: '测试学生',
      role: 'student'
    };

    const testTodosData = {
      progress: {
        total: 6,
        completed: 1,
        pending: 3,
        processing: 2,
        percentage: 17
      },
      recentTodos: [
        {
          id: 1,
          title: '身份验证',
          titleEn: 'Identity Verification',
          status: 'completed',
          updatedAt: '2024-12-01 10:30:00'
        },
        {
          id: 2,
          title: '学费缴纳',
          titleEn: 'Tuition Payment',
          status: 'pending',
          updatedAt: '2024-12-01 09:15:00'
        },
        {
          id: 3,
          title: '体检报告提交',
          titleEn: 'Medical Report Submission',
          status: 'processing',
          updatedAt: '2024-11-30 16:45:00'
        }
      ]
    };

    this.setData({
      userInfo: testUserInfo,
      ...testTodosData,
      loading: false
    });

    console.log('测试数据加载完成');
  },

  // 加载数据
  async loadData() {
    try {
      this.setData({ loading: true });
      
      // 并行加载用户信息和待办事项数据
      const [userInfo, todosData] = await Promise.all([
        this.loadUserInfo(),
        this.loadTodosData()
      ]);
      
      this.setData({
        userInfo,
        ...todosData,
        loading: false
      });
      
    } catch (error) {
      console.error('加载数据失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: t('common.error'),
        icon: 'error'
      });
    }
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const response = await app.request({
        url: '/auth/me'
      });
      
      app.globalData.userInfo = response.user;
      wx.setStorageSync('userInfo', response.user);
      
      return response.user;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return app.globalData.userInfo;
    }
  },

  // 加载待办事项数据
  async loadTodosData() {
    try {
      // 调用实际的API
      const response = await app.request({
        url: '/todos/student/progress'
      });

      if (response.success) {
        return response;
      } else {
        throw new Error(response.error || '获取数据失败');
      }
    } catch (error) {
      console.error('获取待办事项数据失败:', error);

      // 如果API调用失败，返回模拟数据作为备用
      const mockData = {
        progress: {
          total: 6,
          completed: 3,
          pending: 2,
          processing: 1,
          percentage: 50
        },
        recentTodos: [
          {
            id: 1,
            title: '身份验证',
            titleEn: 'Identity Verification',
            status: 'completed',
            updatedAt: '2024-12-01 10:30:00'
          },
          {
            id: 2,
            title: '学费缴纳',
            titleEn: 'Tuition Payment',
            status: 'pending',
            updatedAt: '2024-12-01 09:15:00'
          },
          {
            id: 3,
            title: '体检报告提交',
            titleEn: 'Medical Report Submission',
            status: 'processing',
            updatedAt: '2024-11-30 16:45:00'
          }
        ]
      };

      return mockData;
    }
  },

  // 跳转到待办事项列表
  goToTodos() {
    // 如果未登录，先提示登录
    if (!app.globalData.token) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再查看待办事项',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    wx.switchTab({
      url: '/pages/todos/todos'
    });
  },

  // 跳转到待办事项详情
  goToTodoDetail(e) {
    // 如果未登录，先提示登录
    if (!app.globalData.token) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再查看详情',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/todo-detail/todo-detail?id=${id}`
    });
  },

  // 跳转到个人中心
  goToProfile() {
    // 如果未登录，先提示登录
    if (!app.globalData.token) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再查看个人中心',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  // 跳转到登录页
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 获取本地化标题
  getLocalizedTitle(item) {
    return this.data.language === 'en_us' && item.titleEn ? item.titleEn : item.title;
  },

  // 获取状态文本
  getStatusText(status) {
    return t(`status.${status}`);
  },

  // 格式化时间
  formatTime(timeString) {
    const date = new Date(timeString);
    const now = new Date();
    const diff = now - date;
    
    // 小于1小时显示分钟
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return `${minutes}${t('common.minutes_ago')}`;
    }
    
    // 小于24小时显示小时
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `${hours}${t('common.hours_ago')}`;
    }
    
    // 显示日期
    return date.toLocaleDateString();
  }
});
