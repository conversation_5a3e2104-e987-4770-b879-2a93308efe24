const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');

// 导入配置
const config = require('./src/config/app');
const { testConnection } = require('./src/config/database');

// 导入模型
const { syncModels } = require('./src/models');

// 创建Express应用
const app = express();
const server = http.createServer(app);

// 创建Socket.IO实例
const io = socketIo(server, {
  cors: {
    origin: config.cors.origin,
    methods: ["GET", "POST"],
    credentials: true
  }
});

// 中间件配置
app.use(helmet()); // 安全头
app.use(compression()); // 压缩响应
app.use(cors(config.cors)); // CORS配置

// 请求日志
if (config.env === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// 限流配置
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED'
  }
});
app.use('/api/', limiter);

// 解析请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 根路径欢迎页面
app.get('/', (req, res) => {
  res.json({
    message: '欢迎使用入学待办事项系统API',
    version: '1.0.0',
    environment: config.env,
    endpoints: {
      health: '/health',
      auth: '/api/auth/*',
      todos: '/api/todos/*',
      users: '/api/users/*',
      templates: '/api/templates/*',
      files: '/api/files/*',
      i18n: '/api/i18n/*'
    },
    documentation: 'API文档请访问相应的端点'
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.env,
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API路由
app.use('/api/auth', require('./src/routes/auth'));
app.use('/api/users', require('./src/routes/users'));
app.use('/api/todos', require('./src/routes/todos'));
app.use('/api/templates', require('./src/routes/templates'));
app.use('/api/files', require('./src/routes/files'));
app.use('/api/i18n', require('./src/routes/i18n'));

// Socket.IO连接处理
io.on('connection', (socket) => {
  console.log(`🔌 用户连接: ${socket.id}`);
  
  // 用户加入房间（按角色分组）
  socket.on('join_room', (data) => {
    const { userId, role } = data;
    socket.join(`${role}_room`);
    socket.userId = userId;
    socket.userRole = role;
    console.log(`👤 用户 ${userId} 加入 ${role} 房间`);
  });
  
  // 处理断开连接
  socket.on('disconnect', () => {
    console.log(`🔌 用户断开连接: ${socket.id}`);
  });
});

// 将io实例添加到app中，供路由使用
app.set('io', io);

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    code: 'ROUTE_NOT_FOUND',
    path: req.originalUrl
  });
});

// 全局错误处理
app.use((err, req, res, next) => {
  console.error('❌ 服务器错误:', err);
  
  // 开发环境返回详细错误信息
  const errorResponse = {
    error: config.env === 'development' ? err.message : 'Internal server error',
    code: err.code || 'INTERNAL_ERROR'
  };
  
  if (config.env === 'development') {
    errorResponse.stack = err.stack;
  }
  
  res.status(err.status || 500).json(errorResponse);
});

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    await testConnection();

    // 尝试同步数据库模型
    try {
      await syncModels(false); // 设置为true会重新创建表
      console.log('✅ 数据库模型同步成功');
    } catch (error) {
      console.error('❌ 数据库模型同步失败:', error.message);
      console.log('⚠️  数据库功能将不可用，但服务器继续启动');
    }

    // 启动服务器
    server.listen(config.port, () => {
      console.log(`🚀 服务器启动成功!`);
      console.log(`📍 地址: http://localhost:${config.port}`);
      console.log(`🌍 环境: ${config.env}`);
      console.log(`📊 健康检查: http://localhost:${config.port}/health`);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error.message);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

// 启动服务器
startServer();
