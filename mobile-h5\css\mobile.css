/* 移动端专用样式 */

/* 登录页面 */
.login-container {
    max-width: 400px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.login-header h2 {
    font-size: var(--font-size-xxl);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.login-header p {
    color: var(--gray-600);
    font-size: var(--font-size-base);
}

.login-form {
    background-color: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: var(--spacing-lg);
}

.login-footer {
    text-align: center;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* 欢迎卡片 */
.welcome-card {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border: none;
}

.welcome-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
}

.welcome-text h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.welcome-text p {
    font-size: var(--font-size-base);
    opacity: 0.9;
    margin: 0;
}

.welcome-avatar {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
}

/* 进度卡片 */
.progress-content {
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.progress-circle {
    position: relative;
    width: 120px;
    height: 120px;
    flex-shrink: 0;
}

.progress-svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-bg {
    fill: none;
    stroke: var(--gray-200);
    stroke-width: 8;
}

.progress-bar {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 8;
    stroke-linecap: round;
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
    transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.progress-text span {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
}

.progress-stats {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
}

.stat-number {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* 待办事项列表 */
.todos-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.todo-item {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    border-left: 4px solid var(--gray-300);
}

.todo-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.todo-item.status-completed {
    border-left-color: var(--success-color);
    opacity: 0.8;
}

.todo-item.status-processing {
    border-left-color: var(--warning-color);
}

.todo-item.status-pending {
    border-left-color: var(--info-color);
}

.todo-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.todo-title {
    font-weight: 600;
    color: var(--gray-800);
    flex: 1;
    margin-right: var(--spacing-md);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.status-completed {
    background-color: var(--success-color);
    color: var(--white);
}

.status-badge.status-processing {
    background-color: var(--warning-color);
    color: var(--gray-800);
}

.status-badge.status-pending {
    background-color: var(--info-color);
    color: var(--white);
}

.todo-meta {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* 快捷操作 */
.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.action-btn {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: var(--shadow);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.action-icon {
    display: block;
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-sm);
}

.action-text {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--gray-800);
}

/* 个人中心 */
.profile-info {
    text-align: center;
    padding: var(--spacing-xl);
}

.profile-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: var(--font-size-xxl);
    color: var(--white);
}

.profile-details h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--gray-800);
}

.profile-details p {
    color: var(--gray-600);
    margin: 0;
}

.profile-menu {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.menu-item {
    width: 100%;
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    border: none;
    background: none;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid var(--gray-200);
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background-color: var(--gray-100);
}

.menu-icon {
    font-size: var(--font-size-lg);
    margin-right: var(--spacing-md);
    width: 24px;
    text-align: center;
}

.menu-text {
    flex: 1;
    text-align: left;
    font-size: var(--font-size-base);
    color: var(--gray-800);
}

.menu-arrow {
    color: var(--gray-400);
    font-size: var(--font-size-lg);
}

/* 底部导航 */
.app-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-top: 1px solid var(--gray-200);
    display: flex;
    padding: var(--spacing-sm) 0;
    z-index: 100;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-sm);
    border: none;
    background: none;
    cursor: pointer;
    transition: color 0.2s;
    color: var(--gray-600);
}

.nav-item.active {
    color: var(--primary-color);
}

.nav-icon {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.nav-text {
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* 提示框 */
.toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
}

.toast.show {
    opacity: 1;
    visibility: visible;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    padding: var(--spacing-md);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius);
    transition: background-color 0.2s;
}

.modal-close:hover {
    background-color: var(--gray-100);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.modal-footer .btn {
    flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .progress-content {
        flex-direction: column;
        text-align: center;
    }
    
    .progress-stats {
        grid-template-columns: repeat(3, 1fr);
        width: 100%;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .page-content {
        padding: var(--spacing-sm);
    }
    
    .login-container {
        padding: var(--spacing-md);
    }
    
    .login-form {
        padding: var(--spacing-lg);
    }
}
