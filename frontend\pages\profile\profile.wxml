<!--pages/profile/profile.wxml-->
<view class="page-container">
  <view class="container">
    <!-- 用户头像和基本信息 -->
    <view class="profile-header card">
      <view class="avatar-section">
        <view class="avatar-placeholder">👤</view>
        <view class="user-basic-info">
          <text class="user-nickname">{{userInfo.nickname || 'User'}}</text>
          <text class="user-role">
            {{userInfo.role === 'admin' ? (language === 'en_us' ? 'Administrator' : '管理员') :
              userInfo.role === 'super_admin' ? (language === 'en_us' ? 'Super Admin' : '超级管理员') :
              (language === 'en_us' ? 'Student' : '学生')}}
          </text>
          <text wx:if="{{userInfo.studentId}}" class="user-student-id">{{userInfo.studentId}}</text>
        </view>
      </view>
      
      <!-- 语言切换按钮 -->
      <view class="language-switch-btn" bindtap="switchAppLanguage">
        <text class="language-emoji">🌍</text>
        <text class="language-text">{{language === 'en_us' ? '中文' : 'English'}}</text>
      </view>
    </view>

    <!-- 个人信息表单 -->
    <view class="profile-form card">
      <view class="card-header">
        <text class="section-title">
          {{language === 'en_us' ? 'Personal Information' : '个人信息'}}
        </text>
        <view wx:if="{{!editing}}" class="edit-btn" bindtap="toggleEdit">
          <text class="edit-emoji">✏️</text>
          <text class="edit-text">{{language === 'en_us' ? 'Edit' : '编辑'}}</text>
        </view>
      </view>

      <view class="card-body">
        <!-- 真实姓名 -->
        <view class="form-group">
          <text class="form-label">
            {{language === 'en_us' ? 'Real Name' : '真实姓名'}}
          </text>
          <input 
            wx:if="{{editing}}"
            class="form-input"
            value="{{formData.realName}}"
            placeholder="{{language === 'en_us' ? 'Please enter real name' : '请输入真实姓名'}}"
            data-field="realName"
            bindinput="onFormInput"
          />
          <text wx:else class="form-value">{{userInfo.realName || '--'}}</text>
        </view>

        <!-- 手机号 -->
        <view class="form-group">
          <text class="form-label">
            {{language === 'en_us' ? 'Phone Number' : '手机号码'}}
          </text>
          <input 
            wx:if="{{editing}}"
            class="form-input"
            type="number"
            value="{{formData.phone}}"
            placeholder="{{language === 'en_us' ? 'Please enter phone number' : '请输入手机号码'}}"
            data-field="phone"
            bindinput="onFormInput"
          />
          <text wx:else class="form-value">{{userInfo.phone || '--'}}</text>
        </view>

        <!-- 邮箱 -->
        <view class="form-group">
          <text class="form-label">
            {{language === 'en_us' ? 'Email Address' : '邮箱地址'}}
          </text>
          <input 
            wx:if="{{editing}}"
            class="form-input"
            type="email"
            value="{{formData.email}}"
            placeholder="{{language === 'en_us' ? 'Please enter email address' : '请输入邮箱地址'}}"
            data-field="email"
            bindinput="onFormInput"
          />
          <text wx:else class="form-value">{{userInfo.email || '--'}}</text>
        </view>

        <!-- 语言偏好 -->
        <view class="form-group">
          <text class="form-label">
            {{language === 'en_us' ? 'Language Preference' : '语言偏好'}}
          </text>
          <view wx:if="{{editing}}" class="language-options">
            <view 
              class="language-option {{formData.languagePreference === 'zh_cn' ? 'selected' : ''}}"
              data-language="zh_cn"
              bindtap="onLanguageSelect"
            >
              <text class="option-text">中文</text>
              <view wx:if="{{formData.languagePreference === 'zh_cn'}}" class="option-check">✓</view>
            </view>
            <view 
              class="language-option {{formData.languagePreference === 'en_us' ? 'selected' : ''}}"
              data-language="en_us"
              bindtap="onLanguageSelect"
            >
              <text class="option-text">English</text>
              <view wx:if="{{formData.languagePreference === 'en_us'}}" class="option-check">✓</view>
            </view>
          </view>
          <text wx:else class="form-value">
            {{userInfo.languagePreference === 'en_us' ? 'English' : '中文'}}
          </text>
        </view>

        <!-- 编辑模式下的操作按钮 -->
        <view wx:if="{{editing}}" class="form-actions">
          <button class="btn btn-outline" bindtap="cancelEdit">
            {{language === 'en_us' ? 'Cancel' : '取消'}}
          </button>
          <button class="btn btn-primary" bindtap="saveUserInfo">
            {{language === 'en_us' ? 'Save' : '保存'}}
          </button>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 管理员入口 -->
      <view 
        wx:if="{{userInfo.role === 'admin' || userInfo.role === 'super_admin'}}"
        class="menu-item card"
        bindtap="goToAdmin"
      >
        <view class="menu-icon admin-icon">
          <text class="menu-emoji">⚙️</text>
        </view>
        <view class="menu-content">
          <text class="menu-title">
            {{language === 'en_us' ? 'Admin Panel' : '管理后台'}}
          </text>
          <text class="menu-desc">
            {{language === 'en_us' ? 'Manage students and to-dos' : '管理学生和待办事项'}}
          </text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-icon">></text>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="menu-item card logout-item" bindtap="logout">
        <view class="menu-icon logout-icon">
          <text class="menu-emoji">🚪</text>
        </view>
        <view class="menu-content">
          <text class="menu-title logout-title">
            {{language === 'en_us' ? 'Logout' : '退出登录'}}
          </text>
          <text class="menu-desc">
            {{language === 'en_us' ? 'Sign out of your account' : '退出当前账户'}}
          </text>
        </view>
        <view class="menu-arrow">
          <text class="arrow-icon">></text>
        </view>
      </view>
    </view>
  </view>
</view>
