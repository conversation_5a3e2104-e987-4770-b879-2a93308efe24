// pages/login/login.js
const app = getApp();

// 简化的翻译函数
function t(key) {
  const translations = {
    'login.title': '登录',
    'login.login_success': '登录成功',
    'login.login_failed': '登录失败',
    'common.error': '错误',
    'common.confirm': '确定',
    'error.network': '网络连接失败'
  };
  return translations[key] || key;
}

Page({
  data: {
    loading: false,
    language: 'zh_cn'
  },

  onLoad() {
    console.log('登录页面加载');
    this.setData({
      language: app.globalData.language
    });
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: t('login.title')
    });
    
    // 检查是否已经登录
    if (app.globalData.token) {
      this.redirectToHome();
    }
  },

  onShow() {
    // 每次显示页面时检查登录状态
    if (app.globalData.token) {
      this.redirectToHome();
    }
  },

  // 语言变更回调
  onLanguageChange(language) {
    this.setData({ language });
    wx.setNavigationBarTitle({
      title: t('login.title')
    });
  },

  // 测试登录（开发环境使用）
  async handleTestLogin() {
    if (this.data.loading) return;

    try {
      this.setData({ loading: true });

      // 调用测试登录接口
      const response = await app.request({
        url: '/auth/test-login',
        method: 'POST',
        data: {}
      });

      // 保存登录信息
      app.globalData.token = response.token;
      app.globalData.userInfo = response.user;

      wx.setStorageSync('token', response.token);
      wx.setStorageSync('userInfo', response.user);

      // 显示登录成功提示
      wx.showToast({
        title: '测试登录成功',
        icon: 'success',
        duration: 1500
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        this.redirectToHome();
      }, 1500);

    } catch (error) {
      console.error('测试登录失败:', error);

      wx.showModal({
        title: '登录失败',
        content: '测试登录失败，请检查网络连接',
        showCancel: false,
        confirmText: '确定'
      });

    } finally {
      this.setData({ loading: false });
    }
  },

  // 微信登录
  async handleWechatLogin() {
    if (this.data.loading) return;
    
    try {
      this.setData({ loading: true });
      
      // 获取微信授权码
      const loginRes = await this.promisify(wx.login)();
      if (!loginRes.code) {
        throw new Error('获取微信授权码失败');
      }
      
      // 获取用户信息授权
      const userInfoRes = await this.promisify(wx.getUserProfile)({
        desc: '用于完善用户资料'
      });
      
      // 调用后端登录接口
      const response = await app.request({
        url: '/auth/wechat-login',
        method: 'POST',
        data: {
          code: loginRes.code,
          userInfo: userInfoRes.userInfo
        }
      });
      
      // 保存登录信息
      app.globalData.token = response.token;
      app.globalData.userInfo = response.user;
      
      wx.setStorageSync('token', response.token);
      wx.setStorageSync('userInfo', response.user);
      
      // 显示登录成功提示
      wx.showToast({
        title: t('login.login_success'),
        icon: 'success',
        duration: 1500
      });
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        this.redirectToHome();
      }, 1500);
      
    } catch (error) {
      console.error('登录失败:', error);
      
      let errorMessage = t('login.login_failed');
      
      // 根据错误类型显示不同的提示
      if (error.message.includes('getUserProfile')) {
        errorMessage = this.data.language === 'en_us' ? 
          'User authorization required' : '需要用户授权';
      } else if (error.message.includes('网络')) {
        errorMessage = t('error.network');
      }
      
      wx.showModal({
        title: t('common.error'),
        content: errorMessage,
        showCancel: false,
        confirmText: t('common.confirm')
      });
      
    } finally {
      this.setData({ loading: false });
    }
  },

  // 跳转到首页
  redirectToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 切换语言
  switchLanguage() {
    const currentLanguage = this.data.language;
    const newLanguage = currentLanguage === 'zh_cn' ? 'en_us' : 'zh_cn';
    
    app.setLanguage(newLanguage);
    this.setData({ language: newLanguage });
    
    // 更新导航栏标题
    wx.setNavigationBarTitle({
      title: t('login.title')
    });
  },

  // Promise化微信API
  promisify(fn) {
    return (options = {}) => {
      return new Promise((resolve, reject) => {
        fn({
          ...options,
          success: resolve,
          fail: reject
        });
      });
    };
  }
});
