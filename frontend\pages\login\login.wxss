/* pages/login/login.wxss */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 300rpx;
  height: 300rpx;
  top: 10%;
  right: -100rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 200rpx;
  height: 200rpx;
  top: 60%;
  left: -50rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 150rpx;
  height: 150rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 主要内容 */
.login-content {
  position: relative;
  z-index: 1;
  padding: 80rpx 48rpx 48rpx;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo-container {
  margin-bottom: 48rpx;
}

.logo-placeholder {
  width: 160rpx;
  height: 160rpx;
  border-radius: 32rpx;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
}

.title-container {
  color: white;
}

.app-title {
  display: block;
  font-size: 56rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  font-weight: 300;
  opacity: 0.9;
}

/* 功能介绍 */
.features-section {
  flex: 1;
  margin-bottom: 80rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 48rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 32rpx;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-emoji {
  font-size: 48rpx;
}

.feature-text {
  flex: 1;
  color: white;
}

.feature-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.feature-desc {
  display: block;
  font-size: 26rpx;
  opacity: 0.8;
  line-height: 1.4;
}

/* 登录区域 */
.login-section {
  margin-bottom: 48rpx;
}

.login-button {
  width: 100%;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border: none;
  border-radius: 50rpx;
  padding: 0;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.login-button:not(.loading):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.login-button.loading {
  opacity: 0.8;
}

.button-content,
.button-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

.wechat-emoji {
  font-size: 48rpx;
  margin-right: 16rpx;
}

.button-loading {
  gap: 16rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 语言切换 */
.language-switch {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.language-switch:active {
  background: rgba(255, 255, 255, 0.2);
}

.language-emoji {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.language-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 底部信息 */
.footer-section {
  text-align: center;
}

.footer-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  line-height: 1.5;
}
